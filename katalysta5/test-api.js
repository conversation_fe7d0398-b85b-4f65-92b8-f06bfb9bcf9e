// Test script to verify Gemini API connectivity
import { GoogleGenAI } from "@google/genai";

const API_KEY = "AIzaSyAEn_i7_7zucQpLKqK9Z4vET6KFxD_4aM8";

console.log("🔧 Testing Gemini API connectivity...");
console.log("🔧 API Key:", API_KEY ? "Present" : "Missing");

if (!API_KEY) {
  console.error("❌ API_KEY is missing!");
  process.exit(1);
}

const ai = new GoogleGenAI({ apiKey: API_KEY });

async function testTextGeneration() {
  try {
    console.log("🔧 Testing text generation...");
    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash-preview-04-17',
      contents: [{ role: "user", parts: [{text: "Hello, can you respond with 'API is working'?"}] }],
    });
    console.log("✅ Text generation successful:", response.text);
  } catch (error) {
    console.error("❌ Text generation failed:", error.message);
  }
}

async function testImageGeneration() {
  try {
    console.log("🔧 Testing Gemini 2.0 Flash image generation...");
    const response = await ai.models.generateContent({
      model: 'gemini-2.0-flash-preview-image-generation',
      contents: 'Generate a simple red circle on white background',
      config: {
        responseModalities: ["TEXT", "IMAGE"],
      },
    });

    console.log("🔧 Image response:", !!response);
    console.log("🔧 Response candidates:", response.candidates?.length || 0);

    if (response.candidates && response.candidates.length > 0) {
      const parts = response.candidates[0].content.parts;
      console.log("🔧 Response parts:", parts?.length || 0);

      for (const part of parts) {
        if (part.inlineData) {
          console.log("✅ Image generation successful, size:", part.inlineData.data?.length || 0);
          return;
        }
      }
      console.log("❌ No image data found in response parts");
    } else {
      console.log("❌ No candidates in response");
    }
  } catch (error) {
    console.error("❌ Gemini 2.0 Flash image generation failed:", error.message);
    console.error("❌ Full error:", error);
  }
}

async function listAvailableModels() {
  try {
    console.log("🔧 Listing available models...");
    const models = await ai.models.list();
    console.log("📋 Available models:");
    models.forEach(model => {
      console.log(`  - ${model.name} (${model.supportedGenerationMethods?.join(', ') || 'unknown methods'})`);
    });
  } catch (error) {
    console.error("❌ Failed to list models:", error.message);
  }
}

async function runTests() {
  await testTextGeneration();
  await listAvailableModels();
  await testImageGeneration();
}

runTests();
