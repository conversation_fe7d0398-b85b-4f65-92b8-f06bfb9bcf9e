
# Prompt for Reconstructing the AI Project Catalyst Application

## 1. High-Level Overview

**Application Name:** AI Project Catalyst

**Core Purpose:**
Develop an intelligent Single Page Application (SPA) for managing projects. The application should leverage AI (specifically Google Gemini API) for various insights, strategic suggestions, content generation (text and images), and to assist users in conceptualizing new application ideas. It aims to provide an inspiring and efficient project management experience, catalyzing user success and fostering innovation.

**Key Technologies:**
*   **Frontend:** React with TypeScript, Tailwind CSS for styling.
*   **Routing:** `react-router-dom` (using `HashRouter`).
*   **AI Integration:** `@google/genai` for interacting with Google Gemini API (Flash for text, Imagen for images).
*   **State Management:** React Context API (primarily through a custom `useProjects` hook).
*   **Internationalization:** `i18next` with `react-i18next` and `i18next-browser-languagedetector`.
*   **Utilities:** `uuid` for generating unique IDs, `jszip` for file compression.
*   **Visualization**: `reactflow` for knowledge graphs.

**Core Architectural Principles:**
*   **Modularity:** Components should be small, focused, and reusable. Utilize custom hooks for complex logic.
*   **Responsiveness:** The application must be fully responsive, adapting to various screen sizes from mobile to large desktops.
*   **Accessibility (A11Y):** Adhere to A11Y best practices, using semantic HTML and ARIA attributes where necessary.
*   **User Experience (UX):** Prioritize intuitive navigation, clear feedback for asynchronous operations (loading states, notifications), and a visually appealing interface.
*   **Internationalization (i18n):** All user-facing text must be translatable. AI interactions should also adapt to the selected language.
*   **Error Handling:** Implement robust error handling for API calls and user interactions, providing clear feedback via non-blocking notifications.

## 2. File Structure & Core Setup

Create the following file and directory structure:

*   `index.html`:
    *   Main HTML shell.
    *   Include Tailwind CSS via CDN and configure it with a custom theme (see section 9 for color palette).
    *   Define global styles (e.g., for `body`, inputs, React Flow customisations).
    *   Setup an import map for ESM modules (`react`, `react-dom`, `@google/genai`, etc.).
*   `index.tsx`:
    *   React application entry point.
    *   Render the main `App` component.
    *   Wrap `App` with `HashRouter`, `NotificationProvider`, and `ProjectsProvider`.
    *   Initialize `i18next` asynchronously before rendering.
*   `metadata.json`: Store application name, description.
*   `src/App.tsx`:
    *   Main application component.
    *   Defines routes using `react-router-dom`.
    *   Includes `Header` and `Footer` components.
    *   Integrates `NotificationToastContainer`.
*   `src/types.ts`: Define all TypeScript interfaces and enums for the application (see section 8).
*   `src/config/`: Directory for all configuration files.
    *   `models.ts`: Define AI model names (e.g., `MODELS.TEXT`, `MODELS.IMAGE`).
    *   `icons.tsx`: Export SVG icons as React components.
    *   `fileConfig.ts`: Define supported file types for upload/display.
    *   `prompts/appPrompts.ts`: Meta-prompts for app idea generation.
    *   `prompts/assetPrompts.ts`: Meta-prompts for generating assets like dev specs, blog posts, landing pages, logos.
    *   `forms/formTypes.ts`: TypeScript types for form field definitions.
    *   `forms/logoBriefFormConfig.ts`: Configuration for the logo brief form.
    *   `dynamicAppFormConfig.ts`: Configuration for the dynamic app generator form.
    *   `index.ts`: Barrel file to export all configurations.
*   `src/i18n.ts`:
    *   Setup `i18next` instance.
    *   Implement asynchronous loading of translation JSON files from `/src/locales/`.
    *   Configure language detection (localStorage, navigator) and fallback language.
*   `src/locales/en/translation.json`, `src/locales/sk/translation.json`: Translation files for English and Slovak.
*   `src/services/geminiService.ts`: All communication logic with the Google Gemini API (see section 6).
*   `src/hooks/useProjects.ts`: Custom hook for managing global project state (see section 5).
*   `src/contexts/NotificationContext.tsx`: React context for managing and displaying notifications.
*   `src/components/`: Directory for all React components.
    *   Shared components: `LoadingSpinner.tsx`, `MarkdownRenderer.tsx`, `FileUpload.tsx`.
    *   Layout: `Header.tsx`.
    *   Dashboard: `Dashboard.tsx`, `ProjectCard.tsx`, `ProjectFilterPanel.tsx`, `QuickViewModal.tsx`.
    *   Project Page:
        *   `ProjectPage/ProjectPage.tsx` (main orchestrator).
        *   `ProjectPage/ProjectSideNav.tsx`.
        *   `ProjectPage/sections/`: Sub-components for each section (e.g., `ProjectHeaderSection.tsx`, `AIStrategicCenterSection.tsx`, `ProjectFilesSection.tsx`, `GeneratedImageDisplay.tsx`, etc.).
        *   `ProjectPage/hooks/`: Custom hooks specific to ProjectPage logic (e.g., `useProjectPageModals.ts`, `useProjectPageAssetGeneration.ts`, `useProjectPageImages.ts`).
        *   `ProjectPage/projectPageUtils.ts`: Utility functions for ProjectPage.
        *   `ProjectPage/ProjectPage.types.ts`: Type definitions specific to ProjectPage.
    *   AI Assistant:
        *   `AIAssistantPanel/AIAssistantPanel.tsx` (main orchestrator).
        *   `AIAssistantPanel/hooks/`: Custom hooks for panel logic (tabs, prompts, notes).
        *   `AIAssistantPanel/subcomponents/`: Sub-components (`PromptList.tsx`, `NoteList.tsx`, `InteractionArea.tsx`).
    *   App Idea Generators: `NewAppIdeaPage.tsx`, `ProAppPage.tsx`, `DynamicAppPage.tsx`.
        *   `DeveloperPromptTools/`: Shared hook (`useDeveloperPromptGenerator.ts`) and component (`DeveloperPromptDisplayCard.tsx`) for these pages.
    *   Modals for Asset Generation: `LogoGenerationModal.tsx`, `AIDeveloperSpecGeneratorModal.tsx`, `BlogPostGeneratorModal.tsx`, `LandingPageGeneratorModal.tsx`.
    *   Static Pages: `HowAIWorksPage.tsx`.
    *   Notifications: `NotificationToastContainer.tsx`.

## 3. Core Features and Components

### 3.1. Header (`Header.tsx`)
*   Display application title (translatable, linked to Dashboard).
*   Navigation links (translatable): Dashboard, New Idea, Pro App, Dynamic App, AI Capabilities.
*   Language switcher (SK/EN buttons).
*   Fully responsive mobile menu (hamburger icon transforming to X, menu panel slides in/out).

### 3.2. Footer (`App.tsx`)
*   Copyright notice with current year (translatable).
*   Link to "How AI Works" page (translatable).

### 3.3. Dashboard (`Dashboard.tsx`)
*   **Project Listing**:
    *   Display projects using `ProjectCard.tsx`.
    *   Sort projects (default: recently active/updated, options: created date, name).
    *   Implement `ProjectFilterPanel.tsx` for filtering by:
        *   Search term (name, description, AI keywords, AI type guess).
        *   Status (from `Project.status`).
        *   Favorite (yes/no/all).
    *   Empty state message when no projects exist.
*   **Project Creation**:
    *   "New Project" button opens a modal.
    *   Modal form: Project Name (required), Project Description (required), Conceptual File Upload (`FileUpload.tsx`).
    *   On submit, call `addProject` from `useProjects` hook.
*   **AI Portfolio Overview**:
    *   Display AI-generated insights about the portfolio (synergies, conflicts, shared resources).
    *   "Refresh Analysis" button to re-trigger portfolio analysis.
    *   Clicking on a portfolio insight item should highlight the relevant project cards.
*   **Quick View Modal (`QuickViewModal.tsx`)**:
    *   Triggered from `ProjectCard`.
    *   Displays project name, description, dates, summary of initial AI analysis (type guess, summary, keywords), and a brief summary of strategic insights if available.
    *   Buttons to close modal or navigate to the full project page.
*   **`ProjectCard.tsx`**:
    *   Displays project name, description (line-clamped), status (with color-coded bar and text), AI type guess, AI summary (highlighted if contains risk/opportunity keywords), top AI keywords.
    *   Link to full project page.
    *   Favorite toggle button (star icon).
    *   "Quick View" button.
    *   Visual indication if highlighted by portfolio analysis interaction.

### 3.4. Project Page (`ProjectPage.tsx`)
*   **Master Layout**:
    *   Uses a two-column layout on desktop: `ProjectSideNav.tsx` on the left, main content area on the right.
    *   Main content area shows `ProjectHeaderSection.tsx` fixed at the top, followed by the currently active section.
*   **`ProjectSideNav.tsx`**:
    *   Vertical list of available project sections (translatable labels, icons).
    *   Highlights the active section.
    *   Indicates sections with new AI-generated insights using a small dot/pulse.
    *   On mobile, collapses to a floating action button that opens a bottom sheet menu.
*   **Active Section Rendering**:
    *   State `activeSection` determines which content section is rendered below the header.
    *   Implement smooth scrolling or ensure the top of the active section is visible when selected.
*   **Section Components (located in `src/components/ProjectPage/sections/`)**:

    *   **`ProjectHeaderSection.tsx`**:
        *   Displays project name, description, creation/update dates.
        *   "Save Changes" button (conceptual save, shows notification).
        *   Displays current project status (e.g., "Analyzing Narrative...", "Analysis Complete") and error details if any.
    *   **`ProjectBlueprintSection.tsx`**:
        *   Displays content of a project file named like "*_Blueprint.md" or "*_Developer_Prompt.md" using `MarkdownRenderer.tsx`.
        *   Only shown if such a file exists.
    *   **`AIAssetGeneratorSection.tsx`**:
        *   Buttons to open modals for generating:
            *   Logo (`LogoGenerationModal.tsx`)
            *   AI Developer Specification (`AIDeveloperSpecGeneratorModal.tsx`)
            *   Blog Post (`BlogPostGeneratorModal.tsx`)
            *   Landing Page (`LandingPageGeneratorModal.tsx`)
        *   Buttons should be disabled if any asset generation is globally in progress.
    *   **`AIDeveloperSpecDisplaySection.tsx`**:
        *   Displays content of the AI Developer Spec file (Markdown) using `MarkdownRenderer.tsx`.
        *   Shows loading state if `isGeneratingAIDevSpecLocal` is true.
    *   **`BlogPostDisplaySection.tsx`**:
        *   Displays content of the Blog Post file (Markdown) using `MarkdownRenderer.tsx`.
        *   Shows loading state if `isGeneratingBlogPostLocal` is true.
    *   **`LandingPageDisplaySection.tsx`**:
        *   Displays Landing Page Specification (Markdown) using `MarkdownRenderer.tsx`.
        *   Displays a preview of the Landing Page HTML content in an `iframe`.
        *   Shows loading state if `isGeneratingLandingPageLocal` is true.
    *   **`AIStrategicCenterSection.tsx`**:
        *   Button to trigger "Deep Semantic Analysis & Generate Insights" (`runDeepAnalysis` from `useProjects`).
        *   Displays detailed loading/status messages for different stages of deep analysis (e.g., "Generating Narrative...", "Building Knowledge Graph...").
    *   **`ProjectNarrativeSection.tsx`**:
        *   Displays AI-generated project narrative from `project.strategicOutput.projectPageNarrative`.
    *   **Generated Images Section (Wrapper in `ProjectPage.tsx` using `GeneratedImageDisplay.tsx`)**:
        *   Use `GeneratedImageDisplay.tsx` to show:
            *   Main Project Image (`project.strategicOutput.generatedImage`).
            *   Project Logo (`project.strategicOutput.generatedLogo`).
            *   (Future: Icon, Banner).
        *   `GeneratedImageDisplay.tsx` should:
            *   Show the image if URL exists.
            *   Show a "Generate" button if no image exists (for Logo, this button opens `LogoGenerationModal`).
            *   Allow showing/hiding the generation prompt.
            *   Allow regenerating the image.
            *   Allow generating alternatives and selecting an alternative.
            *   Handle loading states for generation and alternatives.
    *   **`KnowledgeGraphDisplaySection.tsx`**:
        *   Uses `KnowledgeGraphVisualizer.tsx` to display AI-generated knowledge graph from `project.strategicOutput.knowledgeGraph`.
        *   `KnowledgeGraphVisualizer.tsx` should:
            *   Display AI summary of the graph.
            *   Render nodes and edges using `reactflow`. Nodes are draggable.
            *   Include React Flow controls (zoom, fit view) and minimap.
            *   Handle cases with no valid elements or no elements to display with appropriate messages.
            *   Nodes styled differently for "entity" vs "concept".
    *   **`StrategicSuggestionsSection.tsx`**:
        *   Displays AI-generated strategic suggestions (`project.strategicOutput.strategicSuggestions`) using `AISuggestionCard.tsx`.
        *   `AISuggestionCard.tsx` styles suggestions based on type (opportunity, risk, next_step, synergy) and priority.
    *   **`DisplayTemplateSection.tsx`**:
        *   Allows user to select a display template for the project (`project.selectedTemplate`).
        *   Displays AI-suggested template (`project.strategicOutput.suggestedTemplate`).
        *   Selection is conceptual for this demo.
    *   **`AIAssistantPanel.tsx` (Component, not a direct "section" but part of the page content flow)**:
        *   **Tabs**: "Basic Prompts", "Super Assistant", "Saved Notes".
        *   **Prompt Lists**:
            *   Basic: Predefined general prompts (summarize, keywords, explain).
            *   Super Assistant: Dynamically loaded project-specific prompts from `project.strategicOutput.suggestedAssistantPrompts` (available after deep analysis).
        *   **Interaction Area**:
            *   Input field for user query if prompt `requiresUserInput`.
            *   Textarea for additional context if prompt `requiresContextInput`.
            *   "Execute Prompt" button.
            *   Displays AI response.
            *   Allows saving AI response as a note (with a title).
        *   **Saved Notes List**:
            *   Displays saved notes for the project.
            *   Allows creating a Markdown file from a note.
        *   Refactor internal logic into custom hooks (`useAIAssistantTabs`, `useAIAssistantPrompts`, `useAIAssistantNotes`) and sub-components (`PromptList`, `NoteList`, `InteractionArea`).
    *   **`ProjectFilesSection.tsx`**:
        *   Lists all project files (`project.files`).
        *   "Download All as ZIP" button (uses `jszip`).
        *   For each file, provide actions:
            *   Preview (`FilePreviewModal.tsx`): Renders Markdown, HTML (iframe), images, plain text.
            *   Edit (`FileEditModal.tsx`): Allows editing content of text-based files (txt, md, json, html).
            *   Copy Content: Copies file content to clipboard if text-based.
            *   Duplicate File.
            *   Delete File.
            *   Download individual file (if content exists).
*   **Modals for Asset Generation**:
    *   **`LogoGenerationModal.tsx`**:
        *   Form for logo brief (`LogoBriefData` type, config from `logoBriefFormConfig.ts`).
        *   "AI Brief Assistant" button to pre-fill form using AI.
        *   "Generate Logo" button triggers logo generation via `geminiService` and updates project state.
        *   Displays the generated logo within the modal.
    *   **`AIDeveloperSpecGeneratorModal.tsx`**:
        *   Form for AI Developer Specification details (`AIDevSpecFormData`).
        *   "Fill with AI" button.
        *   "Generate" button triggers asset generation.
    *   **`BlogPostGeneratorModal.tsx`**:
        *   Form for Blog Post details (`BlogPostFormData`).
        *   "Fill with AI" button.
        *   "Generate" button.
    *   **`LandingPageGeneratorModal.tsx`**:
        *   Form for Landing Page details (`LandingPageFormData`).
        *   "Fill with AI" button.
        *   "Generate" button (generates Markdown spec and then HTML).

### 3.5. App Idea Generation Pages
*   Shared hook `useDeveloperPromptGenerator.ts` manages state for prompt generation, API calls, errors, copy success.
*   Shared component `DeveloperPromptDisplayCard.tsx` displays the generated prompt, loading/error states, copy button, and "Create Project" button.
*   **`NewAppIdeaPage.tsx`**:
    *   Generates a random app idea and developer blueprint using AI (`OPTIMIZED_APP_IDEA_META_PROMPT`).
    *   Fetches idea on initial load.
    *   "Generate New Idea" button.
*   **`ProAppPage.tsx`**:
    *   User inputs their app idea/problem.
    *   AI generates a detailed developer prompt based on the input (`META_PROMPT_FOR_UNIVERSAL_APP_CREATION`).
*   **`DynamicAppPage.tsx`**:
    *   User fills a multi-section detailed form (`dynamicAppFormConfig.ts`).
    *   Form fields have tooltips for help.
    *   AI generates a highly tailored developer prompt based on form data (`META_PROMPT_FOR_DYNAMIC_APP_PROMPT_GENERATION`).
*   All three pages allow creating a new project using the generated prompt as the description and basis for a blueprint Markdown file.

### 3.6. `HowAIWorksPage.tsx`
*   Static content page explaining AI capabilities, learning mechanisms (conceptual), and vision.
*   Uses translations for all text.

### 3.7. Notifications (`NotificationContext.tsx`, `NotificationToastContainer.tsx`)
*   Non-blocking toast notifications for success, error, info, warning, and API quota exceeded.
*   Notifications should have icons, titles, messages, and a dismiss button.
*   Auto-dismiss after a configurable duration (longer for critical errors like quota).

## 4. Internationalization (i18n)
*   **Setup (`i18n.ts`)**:
    *   Use `i18next`, `react-i18next`, `i18next-browser-languagedetector`.
    *   Asynchronously load translation JSON files from `/src/locales/en/translation.json` and `/src/locales/sk/translation.json`.
    *   Default language: Slovak (`sk`). Fallback: English (`en`).
    *   Language detection order: `localStorage`, `navigator`, `htmlTag`. Cache in `localStorage`.
*   **Translations**:
    *   All user-facing UI text elements must be wrapped in `t()` function.
    *   Provide comprehensive keys in `translation.json` files for all UI elements, notifications, error messages, form labels, tooltips, placeholders, status messages, etc.
*   **AI Language Adaptation**:
    *   `geminiService` functions must accept a `currentLang` parameter.
    *   Prompts sent to Gemini API must instruct the AI to generate its textual output (summaries, narratives, suggestions, etc.) in the `currentLang` (e.g., "in English", "in Slovak"). Technical terms or code can remain in English.
    *   For HTML generation, the `lang` attribute of the `<html>` tag should be set appropriately.

## 5. State Management (`useProjects.ts`)
*   **`ProjectsContext`**: Provide `projects` array and action functions.
*   **`projects` Array**: Store `Project[]` objects. Persist to `localStorage` on change. Initialize from `localStorage` on load.
*   **Loading States**: `isLoading` (global for major operations like adding project), `isLoadingPortfolio`. Project-specific loading states (e.g., `project.status`, `project.deepAnalysisSubStage`).
*   **Core Functions**:
    *   `addProject(name, description, files, isFromPromptGenerator?)`:
        *   Creates a new project object.
        *   If `isFromPromptGenerator` is true and description contains a blueprint, create a Markdown file from it.
        *   Calls `geminiService.getInitialAnalysis`.
        *   Updates project with analysis results and status.
        *   Handles API errors and updates project status to 'error'.
    *   `getProjectById(id)`: Returns a project by ID.
    *   `updateProjectDataCore(projectId, data)`: Updates `project.dataCore`.
    *   `runDeepAnalysis(projectId)`:
        *   Orchestrates multi-stage deep analysis by calling `geminiService.performDeepAnalysis`.
        *   Updates `project.status`, `project.deepAnalysisSubStage`, and `project.detailedStatusMessageKey` throughout the process for granular UI feedback.
        *   After core analysis, attempts to generate main project image and logo (if brief exists or generic).
        *   Updates project with `AIStrategicOutput`.
        *   Handles API errors.
    *   `setSelectedTemplate(projectId, template)`.
    *   `analyzePortfolio()`: Calls `geminiService.analyzePortfolioSynergies`.
    *   `executeProjectAssistantPrompt(projectId, promptTemplate, userInput, contextInput?)`: Calls `geminiService.executeAssistantPrompt`.
    *   `addAssistantNoteToDataCore(projectId, title, content, promptUsed, contextProvided?)`: Adds an `AIAssistantNote` to `project.dataCore.assistantNotes`.
    *   `toggleFavoriteProject(projectId)`.
    *   `addConceptualFileToProject(projectId, fileName, fileType, fileSize, fileContent)`: Adds or updates a file in `project.files`, primarily for AI-generated content.
    *   `updateProjectGeneratedImage(projectId, imageType, details)`: Updates `project.strategicOutput` with `GeneratedImageDetails` and adds/updates corresponding image file in `project.files`.
    *   `generateAndSetProjectLogo(projectId, brief)`: Orchestrates logo generation using `geminiService.generateLogoFromBrief` and updates state.
    *   `fetchAIAssistedLogoBrief(projectId)`: Calls `geminiService` for AI-assisted brief filling.
    *   `saveLogoBriefToDataCore(projectId, brief)`.
    *   `generateAndSaveAIDevSpec(projectId, formData)`, `generateAndSaveBlogPost(projectId, formData)`, `generateAndSaveLandingPage(projectId, formData)`: Use `geminiService.generateGenericAsset` and `geminiService.generateLandingPageHTML`, then add results as project files. Update `project.dataCore` with form data.
    *   `getAIAssistanceForAIDevSpecForm(project)`, `getAIAssistanceForBlogPostForm(project)`, `getAIAssistanceForLandingPageForm(project)`: Call respective `geminiService` methods.
    *   File management: `deleteProjectFile`, `duplicateProjectFile`, `updateProjectFileContent`.
*   **Timestamps**: Ensure `createdAt`, `updatedAt`, and `lastInteractionTimestamp` are managed correctly.

## 6. AI Service Integration (`geminiService.ts`)
*   Initialize `GoogleGenAI` with `API_KEY` from `process.env.API_KEY`. Handle cases where API_KEY is missing.
*   **`parseJsonSafe<T>(jsonString, context)`**:
    *   Remove markdown fences (```json ... ```).
    *   Attempt `JSON.parse()`.
    *   Implement specific repair logic for known malformed JSON patterns from AI, conditioned by `context`:
        *   For "Assistant Prompts": Fix `]儂{` to `},{`.
        *   For "Knowledge Graph": Fix extraneous text after `"type": "concept"` lines.
        *   For "Strategic Suggestions": Fix junk text and incorrect separators between array objects.
    *   Log errors with original string for debugging. Return `null` on failure.
*   **`handleApiError(error, lng, defaultErrorKey)`**:
    *   Detect API quota errors (from error message substrings like "quota", "429", "resource has been exhausted") and return specific translatable title/summary.
    *   Return generic error messages if API key is missing or for other errors.
*   **`withRetry(fn, retries, delayMs)`**: Wrapper for API calls to retry on network/RPC errors with exponential backoff.
*   **Prompts**:
    *   All prompts sent to Gemini must be carefully engineered for clarity and desired output format (especially JSON).
    *   Incorporate `{{outputLanguage}}` placeholders in prompts to instruct AI on the language for textual generation.
    *   For JSON outputs, prompts must strictly define the expected JSON structure and emphasize that ONLY the JSON object/array should be returned.
*   **API Function Implementations**:
    *   `getInitialAnalysis(projectName, projectDescription, files, currentLang)`: Returns `AIInitialAnalysis`.
    *   `generateProjectImage(project, currentLang)`: Returns `GeneratedImageDetails`. Uses `MODELS.IMAGE`.
    *   `generateLogo(project, currentLang)`: Generic logo generation. Returns `GeneratedImageDetails`.
    *   `generateLogoFromBrief(brief, project, currentLang)`: Logo from user brief.
    *   `generateImageAlternatives(basePrompt, currentLang, numberOfAlternatives)`: Returns array of base64 image strings.
    *   `performDeepAnalysis(project, currentLang)`: Returns `Omit<AIStrategicOutput, 'generatedImage' | 'generatedLogo' | 'generatedIcon' | 'generatedBanner'>`. Fetches narrative, KG, suggestions, assistant prompts.
    *   `analyzePortfolioSynergies(projects, currentLang)`: Returns `PortfolioAnalysis`.
    *   `executeAssistantPrompt(promptText, contextText, currentLang)`: Returns string response.
    *   `generateAppIdeaDeveloperPrompt(metaPrompt, currentLang)`: Uses `OPTIMIZED_APP_IDEA_META_PROMPT`.
    *   `generateProAppDeveloperPrompt(userInput, metaPromptTemplate, currentLang)`: Uses `META_PROMPT_FOR_UNIVERSAL_APP_CREATION`.
    *   `generateDynamicAppDeveloperPrompt(formData, metaPromptTemplate, currentLang)`: Uses `META_PROMPT_FOR_DYNAMIC_APP_PROMPT_GENERATION`.
    *   `generateDocumentationOutline(blueprintContent, currentLang)`: Uses `DOCUMENTATION_GENERATION_META_PROMPT`.
    *   `generateGenericAsset(project, formData, assetType, currentLang)`: Central function using `GENERIC_ASSET_GENERATION_META_PROMPT` and asset-specific `OUTPUT_SPECS` (for AI Dev Spec, Blog Post, Landing Page Content/Spec).
    *   `generateLandingPageHTML(markdownSpec, currentLang)`: Uses `LANDING_PAGE_HTML_GENERATION_META_PROMPT` to convert Markdown spec to HTML.
    *   `generateAIAssistedLogoBrief(project, currentLang)`: Returns `Partial<LogoBriefData>`.
    *   `generateAIAssistedAIDevSpecForm(project, currentLang)`: Returns `Partial<AIDevSpecFormData>`.
    *   `generateAIAssistedBlogPostForm(project, currentLang)`: Returns `Partial<BlogPostFormData>`.
    *   `generateAIAssistedLandingPageForm(project, currentLang)`: Returns `Partial<LandingPageFormData>`.

## 7. Configuration (`src/config/`)
*   **`models.ts`**: `MODELS.TEXT = 'gemini-2.5-flash-preview-04-17'`, `MODELS.IMAGE = 'imagen-3.0-generate-002'`.
*   **`icons.tsx`**: Provide SVG icons as React components for various UI elements (project, AI sparkle, save, upload, navigation icons, status icons, etc.).
*   **`fileConfig.ts`**: Define `FILE_TYPES_SUPPORTED` (for conceptual uploads) and `FILE_TYPES_SUPPORTED_FILTER` (for UI filters).
*   **`prompts/appPrompts.ts`**: Store meta-prompts like `OPTIMIZED_APP_IDEA_META_PROMPT`, `META_PROMPT_FOR_UNIVERSAL_APP_CREATION`, `META_PROMPT_FOR_DYNAMIC_APP_PROMPT_GENERATION`. These prompts should contain placeholders like `{{outputLanguage}}`, `{{userInput}}`, `{{formDataString}}`.
*   **`prompts/assetPrompts.ts`**: Store meta-prompts like `GENERIC_ASSET_GENERATION_META_PROMPT`, `AI_DEVELOPER_SPEC_OUTPUT_SPECS`, `BLOG_POST_OUTPUT_SPECS`, `LANDING_PAGE_CONTENT_AND_VISUAL_SPEC_OUTPUT_SPECS`, `LOGO_GENERATION_AI_PROMPT_TEMPLATE`, `DOCUMENTATION_GENERATION_META_PROMPT`, `LANDING_PAGE_HTML_GENERATION_META_PROMPT`. These are complex and crucial.
*   **`forms/logoBriefFormConfig.ts`**: Array defining fields for the logo brief form (name, labelKey, type, tooltipKey, options, etc.).
*   **`dynamicAppFormConfig.ts`**: Array defining sections and fields for the dynamic app generator page.

## 8. Core Data Structures (`src/types.ts`)
Define all interfaces and enums. Key structures include:
*   `Project`: id, name, description, files (`ProjectFile[]`), createdAt, updatedAt, initialAnalysis (`AIInitialAnalysis`), strategicOutput (`AIStrategicOutput`), dataCore (projectSummary, keyInsights, knowledgeGraphData (`KnowledgeGraph`), assistantNotes (`AIAssistantNote[]`), logoBrief (`LogoBriefData`), aiDevSpecFormData, blogPostFormData, landingPageFormData, etc.), selectedTemplate (`ProjectTemplate`), status (`ProjectStatus`), deepAnalysisSubStage (`DeepAnalysisSubStage`), errorDetails, isFavorite, lastInteractionTimestamp.
*   `ProjectFile`: id, name, type (MIME), size, content (optional string for text/base64).
*   `AIInitialAnalysis`: projectTypeGuess, keywords, summary.
*   `AIStrategicOutput`: projectPageNarrative, suggestedTemplate, generatedImage/Logo/Icon/Banner (`GeneratedImageDetails`), knowledgeGraph, strategicSuggestions (`AISuggestion[]`), suggestedAssistantPrompts (`AIAssistantPromptCategory[]`).
*   `GeneratedImageDetails`: url, prompt, alternatives (string[] of base64 URLs).
*   `KnowledgeGraph`: elements (`KnowledgeGraphElement[]`), relations (`KnowledgeGraphRelation[]`), summary.
*   `AISuggestion`: id, title, description, type, priority.
*   `ProjectTemplate`: Enum (STANDARD, VISUAL_HEAVY, DATA_FOCUSED, NARRATIVE_RICH).
*   `AIAssistantPromptCategory` & `AIAssistantPrompt`.
*   `AIAssistantNote`.
*   `DeepAnalysisSubStage`: Enum for tracking sub-stages of deep analysis.
*   `ProjectStatus`: Enum for various project states.
*   Form Data Types: `LogoBriefData`, `AIDevSpecFormData`, `BlogPostFormData`, `LandingPageFormData` (detailing all fields).
*   `NotificationMessage`, `NotificationType`, `NotificationContextType`.
*   `PortfolioAnalysis`.
*   `ProjectsContextType`.

## 9. UI/UX Guidelines & Styling
*   **Color Palette (define in `:root` of `index.html`)**:
    *   Primary: `#5E72E4` (Indigo)
    *   Secondary: `#F4F5F7` (Light Gray - page background)
    *   Accent: `#11CDEF` (Cyan)
    *   Base-100: `#FFFFFF` (Card backgrounds, modals)
    *   Neutral: `#232759` (Main text)
    *   Various status colors (info, success, warning, error, pending).
    *   AI-specific functional colors (for opportunities, risks, different AI asset types).
    *   React Flow specific colors.
*   **Typography**: Use 'Inter' font family.
*   **Layout**: Use Tailwind CSS for responsive layouts. Container class for centering content. Consistent padding and margins.
*   **Interactions**: Smooth transitions for modals and menus. Clear hover and focus states for interactive elements (buttons, links, inputs).
*   **Forms**: Consistent styling for inputs, textareas, selects. Clear labels and placeholders. Tooltips for help on complex forms.
*   **Loading States**: Utilize `LoadingSpinner.tsx` with appropriate messages and styles (default spinner, dots, line pulse, icon pulse) for all asynchronous operations.
*   **Empty States**: Provide informative and visually engaging empty states (e.g., no projects on dashboard, no notes in AI assistant).

## 10. Build & Environment
*   Assume a modern JavaScript environment that supports ES modules directly in the browser via import maps (as per `index.html`).
*   API key (`process.env.API_KEY`) is expected to be available in the environment where `geminiService.ts` is executed. The application code should not manage or prompt for this key.

This prompt should provide a comprehensive blueprint. The AI should be instructed to ask clarifying questions if any part of this specification is ambiguous, but the goal is to be as self-contained as possible for a full reconstruction.
Emphasis should be on recreating the *functionality* and *architecture*, not necessarily a pixel-perfect visual copy if minor stylistic deviations occur, as long as Tailwind CSS and the specified color palette are used.
The AI-generated content (prompts for AI, form configurations) needs to be accurately replicated in the `src/config/` directory.
All translatable strings should be placed in `src/locales/en/translation.json` and `src/locales/sk/translation.json`.
The AI should ensure all custom hooks and contexts are implemented correctly to manage application state and side effects as described.
All components should be functional React components using TypeScript.
Adherence to the provided file structure is important.
Pay close attention to the error handling, notification system, and the nuanced states of project analysis.
The interaction between `useProjects`, `geminiService`, and UI components is key.
All AI interaction prompts defined in `src/config/prompts/` must be accurately recreated and used by `geminiService.ts`.
The modular structure of `ProjectPage.tsx` with its side navigation and dynamic section rendering is a critical architectural feature to replicate.
The refactoring of `AIAssistantPanel` into smaller hooks and sub-components should also be followed.
The shared `DeveloperPromptTools` for app idea generation pages are also important.
Ensure all defined types in `src/types.ts` are correctly used throughout the application.
The complex forms for asset generation (Logo, AI Dev Spec, Blog Post, Landing Page) with their AI-fill capabilities need to be implemented as described.
File management features within `ProjectFilesSection` (preview, edit, copy, duplicate, delete, download) are crucial.
