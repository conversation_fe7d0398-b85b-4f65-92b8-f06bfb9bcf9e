import { GoogleGenAI, GenerateContentResponse } from "@google/genai";
import { AIInitialAnalysis, AIStrategicOutput, KnowledgeGraph, AISuggestion, ProjectFile, Project, ProjectTemplate, PortfolioAnalysis } from '../types';
import { MODELS } from '../constants';
import i18n from '../i18n.ts'; // Import i18n instance

const API_KEY = process.env.API_KEY;

if (!API_KEY) {
  console.error("API_KEY for Gemini is not set. Please set process.env.API_KEY.");
}

const ai = API_KEY ? new GoogleGenAI({ apiKey: API_KEY }) : null;

const parseJsonSafe = <T,>(jsonString: string, context: string): T | null => {
  try {
    let cleanJsonString = jsonString.trim();
    const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
    const match = cleanJsonString.match(fenceRegex);
    if (match && match[2]) {
      cleanJsonString = match[2].trim();
    }
    return JSON.parse(cleanJsonString) as T;
  } catch (error) {
    console.error(`Error parsing JSON for ${context}:`, error, "Original string:", jsonString);
    return null;
  }
};


export const geminiService = {
  getInitialAnalysis: async (projectName: string, projectDescription: string, files: ProjectFile[]): Promise<AIInitialAnalysis> => {
    if (!ai) return { 
        projectTypeGuess: i18n.t('serviceMessages.apiKeyMissingError'), 
        keywords: [], 
        summary: i18n.t('serviceMessages.apiKeyMissingErrorSummary') 
    };
    
    const fileSummary = files.map(f => `${f.name} (${f.type}, ${Math.round(f.size/1024)}KB)`).join(', ');
    // Prompts remain in English for AI interaction
    const prompt = `
      Analyze the following project details to provide an initial assessment.
      Project Name: "${projectName}"
      Project Description: "${projectDescription}"
      Associated Files: ${fileSummary || 'No files specified.'}

      Based on this information, please:
      1. Suggest a primary "Project Type" (e.g., Software Development, Research Paper, Marketing Campaign, Creative Product Design, Data Analysis Report).
      2. Extract 5-7 relevant "Keywords" or key themes.
      3. Provide a very brief one-sentence "Summary" of the project's apparent core goal.

      Return your response *ONLY* as a valid JSON object with the following structure. Ensure no extraneous text, comments, or characters appear outside this JSON object. The entire response must be this JSON object and nothing else.
      {
        "projectTypeGuess": "Suggested Project Type",
        "keywords": ["Keyword1", "Keyword2", ...],
        "summary": "One-sentence project summary."
      }
    `;

    try {
      const response: GenerateContentResponse = await ai.models.generateContent({
        model: MODELS.TEXT,
        contents: [{ role: "user", parts: [{text: prompt}] }],
        config: { responseMimeType: "application/json" }
      });
      const resultJson = parseJsonSafe<AIInitialAnalysis>(response.text, "Initial Analysis");
      if (resultJson) {
        return resultJson;
      }
      throw new Error(i18n.t('serviceMessages.parseError'));
    } catch (error) {
      console.error("Error fetching initial analysis:", error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      return {
        projectTypeGuess: i18n.t('serviceMessages.initialAnalysisError'),
        keywords: [],
        summary: i18n.t('serviceMessages.initialAnalysisErrorSummary', { error: errorMessage }),
      };
    }
  },

  generateProjectImage: async (project: Project): Promise<string | undefined> => {
    if (!ai) return undefined;

    const narrativeSummary = project.strategicOutput?.projectPageNarrative?.substring(0, 200) || project.description;
    const keywordsString = project.initialAnalysis?.keywords?.join(', ') || 'general project';
    
    const imagePrompt = `
      Create a visually inspiring and symbolic image representing a project.
      Project Essence: ${narrativeSummary}
      Key Themes: ${keywordsString}
      Project Type: ${project.initialAnalysis?.projectTypeGuess || 'diverse'}
      Desired Style: Abstract, conceptual, vibrant, optimistic, with a sense of innovation and depth. Avoid text.
    `;

    try {
      const response = await ai.models.generateImages({
        model: MODELS.IMAGE,
        prompt: imagePrompt,
        config: { numberOfImages: 1, outputMimeType: 'image/jpeg' },
      });
      if (response.generatedImages && response.generatedImages.length > 0) {
        const base64ImageBytes = response.generatedImages[0].image.imageBytes;
        return `data:image/jpeg;base64,${base64ImageBytes}`;
      }
      return undefined;
    } catch (error) {
      console.error("Error generating project image:", error);
      return undefined;
    }
  },

  performDeepAnalysis: async (project: Project): Promise<Omit<AIStrategicOutput, 'generatedImageUrl'>> => {
    if (!ai) {
      const defaultKnowledgeGraph: KnowledgeGraph = { elements: [], relations: [], summary: i18n.t('serviceMessages.apiKeyMissingError') };
      return {
        projectPageNarrative: i18n.t('serviceMessages.deepAnalysisDefaultNarrative'),
        suggestedTemplate: ProjectTemplate.STANDARD,
        knowledgeGraph: defaultKnowledgeGraph,
        strategicSuggestions: [{ 
            id: 'err1', 
            title: i18n.t('serviceMessages.deepAnalysisDefaultSuggestionTitle'), 
            description: i18n.t('serviceMessages.deepAnalysisDefaultSuggestionDescription'), 
            type: 'risk' 
        }],
      };
    }

    const filesSummary = project.files.map(f => f.name).join(', ');
    const basePromptInfo = `
      Project Name: "${project.name}"
      Project Description: "${project.description}"
      Initial AI Analysis: 
        Type: ${project.initialAnalysis?.projectTypeGuess || 'N/A'}
        Keywords: ${project.initialAnalysis?.keywords?.join(', ') || 'N/A'}
        Summary: ${project.initialAnalysis?.summary || 'N/A'}
      Files: ${filesSummary || 'None specified'}
    `;

    const narrativePrompt = `
      ${basePromptInfo}
      Generate a compelling and "living" project page narrative. This narrative should:
      - Clearly articulate the project's mission and vision.
      - Highlight key objectives and anticipated outcomes/values.
      - Create an engaging story around the project that inspires and informs.
      - Be structured for readability on a webpage (e.g., use paragraphs).
      - Max 300 words.
      Return as a plain text string.
    `;
    
    const kgPrompt = `
      ${basePromptInfo}
      Based on all available information, conceptualize a knowledge graph for this project.
      Identify:
      - Key entities (e.g., specific technologies, people, organizations, core concepts if tangible).
      - Important concepts (abstract ideas, methodologies, principles).
      - Potential relationships between these entities/concepts (e.g., "uses", "depends on", "contributes to", "developed by").
      
      Return this *ONLY* as a valid JSON object with the structure below. Ensure the entire response is exclusively this JSON object, with no extra text, comments, or characters.
      {
        "elements": [ { "id": "unique_id_1", "label": "Entity/Concept Name 1", "type": "entity" | "concept" }, ... ],
        "relations": [ { "source": "id_of_source_element", "target": "id_of_target_element", "label": "Relationship Label" }, ... ],
        "summary": "A brief textual summary of what this knowledge graph represents for the project."
      }
      Keep IDs simple (e.g., e1, c1, r1). Limit to 5-10 elements and 3-7 relations for this conceptual graph.
    `;

    const suggestionsPrompt = `
      ${basePromptInfo}
      Provide 3-5 pro-active, actionable strategic suggestions for this project. These should be insightful and help guide the project towards success.
      Consider:
      - Potential opportunities to explore.
      - Risks to mitigate.
      - Concrete next steps.
      - Potential synergies (if information allows, otherwise general strategic advice).
      - Creative ideas or novel approaches.

      Return *ONLY* a valid JSON array, where each object has: "id" (unique string), "title" (string), "description" (string), "type": ("opportunity" | "risk" | "next_step" | "synergy"), and optionally "priority" ("high" | "medium" | "low"). Ensure the entire response is exclusively this JSON array, with no extra text, comments, or characters.
      Example: [{ "id": "sugg1", "title": "Explore X Technology", "description": "...", "type": "opportunity", "priority": "high" }]
    `;

    try {
      const [narrativeResponse, kgResponse, suggestionsResponse] = await Promise.all([
        ai.models.generateContent({ model: MODELS.TEXT, contents: [{ role: "user", parts: [{text: narrativePrompt}] }] }),
        ai.models.generateContent({ model: MODELS.TEXT, contents: [{ role: "user", parts: [{text: kgPrompt}] }], config: { responseMimeType: "application/json" } }),
        ai.models.generateContent({ model: MODELS.TEXT, contents: [{ role: "user", parts: [{text: suggestionsPrompt}] }], config: { responseMimeType: "application/json" } })
      ]);

      const projectPageNarrative = narrativeResponse.text.trim();
      const knowledgeGraph = parseJsonSafe<KnowledgeGraph>(kgResponse.text, "Knowledge Graph") || { elements: [], relations: [], summary: i18n.t('serviceMessages.knowledgeGraphParseErrorSummary') };
      const strategicSuggestions = parseJsonSafe<AISuggestion[]>(suggestionsResponse.text, "Strategic Suggestions") || [{id: 'parse_err_sugg', title: i18n.t('serviceMessages.suggestionsParseErrorTitle'), description: i18n.t('serviceMessages.suggestionsParseErrorDescription'), type: 'risk'}];
      
      let suggestedTemplate = ProjectTemplate.STANDARD;
      if (project.initialAnalysis?.keywords?.some(k => k.toLowerCase().includes('visual') || k.toLowerCase().includes('design'))) {
        suggestedTemplate = ProjectTemplate.VISUAL_HEAVY;
      } else if (project.initialAnalysis?.keywords?.some(k => k.toLowerCase().includes('data') || k.toLowerCase().includes('report'))) {
        suggestedTemplate = ProjectTemplate.DATA_FOCUSED;
      } else if (projectPageNarrative.length > 150) {
        suggestedTemplate = ProjectTemplate.NARRATIVE_RICH;
      }

      return {
        projectPageNarrative,
        knowledgeGraph,
        strategicSuggestions,
        suggestedTemplate,
      };

    } catch (error) {
      console.error("Error in deep analysis:", error);
      const errorMsg = i18n.t('serviceMessages.deepAnalysisFailedError', { error: error instanceof Error ? error.message : String(error) });
      return {
        projectPageNarrative: errorMsg,
        knowledgeGraph: { elements: [], relations: [], summary: i18n.t('serviceMessages.deepAnalysisFailedError', {error: "KG generation part"}) },
        strategicSuggestions: [{ id: 'deep_err', title: i18n.t('serviceMessages.deepAnalysisFailedError', {error: "Suggestions generation part"}), description: errorMsg, type: 'risk' }],
        suggestedTemplate: ProjectTemplate.STANDARD,
      };
    }
  },
  
  analyzePortfolioSynergies: async (projects: Project[]): Promise<PortfolioAnalysis> => {
    const defaultEmptyAnalysis: PortfolioAnalysis = { synergies: [], conflicts: [], sharedResources: [] };
    if (!ai || projects.length < 2) {
      return defaultEmptyAnalysis;
    }

    const projectSummaries = projects.map(p => 
      `Project ID: ${p.id}, Name: "${p.name}", Type: ${p.initialAnalysis?.projectTypeGuess || 'N/A'}, Keywords: ${p.initialAnalysis?.keywords?.join(', ') || 'N/A'}, Summary: ${p.initialAnalysis?.summary || p.description.substring(0,100)}`
    ).join('\n---\n');

    const prompt = `
      Analyze the following portfolio of projects to identify potential synergies, conflicts, or shared resources.
      Projects:
      ${projectSummaries}

      Focus on:
      1. Synergies: Where projects could benefit each other (e.g., shared technology, overlapping goals, complementary outputs).
      2. Conflicts: Potential areas of competition, resource contention, or contradictory objectives.
      3. Shared Resources: Opportunities to share knowledge, tools, or personnel across projects.

      Return your response *ONLY* as a valid JSON object with the structure below. The entire response must be exclusively this JSON object, with no extra text, comments, or characters.
      {
        "synergies": [{ "projectIds": ["id1", "id2"], "description": "Description of synergy" }, ...],
        "conflicts": [{ "projectIds": ["id1", "id2"], "description": "Description of conflict" }, ...],
        "sharedResources": [{ "resource": "Resource Name", "projectIds": ["id1", "id2", ...], "description": "How it can be shared" }, ...]
      }
      Be concise and highlight only the most significant interactions. If none, return empty arrays for each category.
    `;

    try {
      const response = await ai.models.generateContent({
        model: MODELS.TEXT,
        contents: [{role: "user", parts: [{text: prompt}]}],
        config: { responseMimeType: "application/json" }
      });
      const analysis = parseJsonSafe<PortfolioAnalysis>(response.text, "Portfolio Analysis");
      
      return {
        synergies: analysis?.synergies || [],
        conflicts: analysis?.conflicts || [],
        sharedResources: analysis?.sharedResources || [],
      };
    } catch (error) {
      console.error("Error analyzing portfolio synergies:", error);
      const errorDescription = error instanceof Error ? error.message : String(error);
      return { 
        synergies: [], 
        conflicts: [{ projectIds: [], description: i18n.t('serviceMessages.portfolioAnalysisAPIFail', {error: errorDescription}) }], 
        sharedResources: [] 
      };
    }
  }
};