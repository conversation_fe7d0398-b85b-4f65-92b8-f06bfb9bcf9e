<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI Project Catalyst</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: 'var(--color-primary)',
              secondary: 'var(--color-secondary)',
              accent: 'var(--color-accent)',
              neutral: 'var(--color-neutral)',
              'base-100': 'var(--color-base-100)',
              info: 'var(--color-info)', 
              success: 'var(--color-success)', 
              warning: 'var(--color-warning)', 
              error: 'var(--color-error)', 
              'accent-dark': 'var(--color-accent-dark)',
              
              'status-info': 'var(--color-status-info)',
              'status-success': 'var(--color-status-success)',
              'status-warning': 'var(--color-status-warning)',
              'status-error': 'var(--color-status-error)',
              'status-pending': 'var(--color-status-pending)',

              'ai-opportunity-high': 'var(--color-ai-opportunity-high)',
              'ai-opportunity-medium': 'var(--color-ai-opportunity-medium)',
              'ai-risk-high': 'var(--color-ai-risk-high)',
              'ai-risk-medium': 'var(--color-ai-risk-medium)',
              'ai-next-step': 'var(--color-ai-next-step)',
              'ai-synergy': 'var(--color-ai-synergy)',
              'ai-creative': 'var(--color-ai-creative)',
              'ai-technical': 'var(--color-ai-technical)',
              'ai-research': 'var(--color-ai-research)',
              'ai-developer-prompt': 'var(--color-ai-developer-prompt)',
              'neutral-70': 'var(--color-neutral-70)',
              'neutral-30': 'var(--color-neutral-30)',
            },
            animation: {
              'pulse-fast': 'pulse 1s cubic-bezier(0.4, 0, 0.6, 1) infinite',
              'progress-bar': 'progressBar 2s ease-out infinite', 
              'linear-progress-bar': 'linearProgressBar 1.5s ease-in-out infinite',
              'ai-thinking-dots': 'aiThinkingDots 1.4s infinite ease-in-out both',
              'soft-pulse': 'softPulse 2s infinite cubic-bezier(0.4, 0, 0.6, 1)',
              'line-pulse-h': 'linePulseHorizontal 1.5s ease-in-out infinite',
            },
            keyframes: {
              progressBar: { 
                '0%': { width: '0%', opacity: '0.5' },
                '50%': { width: '100%', opacity: '1' },
                '100%': { width: '0%', opacity: '0.5', transform: 'translateX(100%)' },
              },
              linearProgressBar: { 
                '0%': { transform: 'translateX(-101%)', opacity: '0.7' },
                '50%': { opacity: '1' },
                '100%': { transform: 'translateX(101%)', opacity: '0.7' },
              },
              aiThinkingDots: {
                '0%, 80%, 100%': { transform: 'scale(0)', opacity: '0' },
                '40%': { transform: 'scale(1.0)', opacity: '1' },
              },
              softPulse: {
                '0%, 100%': { opacity: '1', transform: 'scale(1)' },
                '50%': { opacity: '0.7', transform: 'scale(1.05)' },
              },
              linePulseHorizontal: {
                '0%': { width: '20%', opacity: '0.5' },
                '50%': { width: '80%', opacity: '1' },
                '100%': { width: '20%', opacity: '0.5' },
              },
            },
            boxShadow: { 
              'soft-xl': '0 20px 25px -5px rgba(0, 0, 0, 0.08), 0 10px 10px -5px rgba(0, 0, 0, 0.03)',
              'lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
              'xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
              '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
            }
          }
        },
        plugins: [
          function({ addUtilities, theme }) { // Updated to addUtilities
            const newUtilities = {
              '.animate-ai-thinking-dots-1': { animationDelay: '-0.32s' },
              '.animate-ai-thinking-dots-2': { animationDelay: '-0.16s' },
              '.animate-ai-thinking-dots-3': { animationDelay: '0s' },
            }
            addUtilities(newUtilities)
          },
          function({ addComponents, theme }) {
            addComponents({
              '.btn-primary': {
                backgroundColor: theme('colors.primary'),
                color: theme('colors.base-100'),
                '&:hover': {
                  backgroundColor: theme('colors.primary / 0.9'),
                  boxShadow: theme('boxShadow.lg'),
                },
                '&:focus-visible': {
                    outline: `2px solid ${theme('colors.primary / 0.5')}`,
                    outlineOffset: '2px',
                }
              },
              '.btn-accent': {
                backgroundColor: theme('colors.accent'),
                color: theme('colors.base-100'), 
                '&:hover': {
                  backgroundColor: theme('colors.accent / 0.9'),
                  boxShadow: theme('boxShadow.lg'),
                },
                '&:focus-visible': {
                    outline: `2px solid ${theme('colors.accent / 0.5')}`,
                    outlineOffset: '2px',
                }
              },
               '.btn-secondary': { 
                backgroundColor: theme('colors.secondary'),
                color: theme('colors.neutral-70'),
                border: `1px solid ${theme('colors.neutral-30')}`,
                '&:hover': {
                  backgroundColor: theme('colors.neutral-30 / 0.5'), 
                  boxShadow: theme('boxShadow.md'),
                },
                '&:focus-visible': {
                    outline: `2px solid ${theme('colors.neutral-70 / 0.5')}`,
                    outlineOffset: '2px',
                }
              },
            })
          },
        ]
      }; 
    </script>
    <style>
      @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
      @import "https://esm.sh/reactflow/dist/style.css";

      :root {
        /* Base & UI Colors (Updated) */
        --color-primary: #5E72E4; /* Indigo */
        --color-secondary: #F4F5F7; /* Light Gray */
        --color-accent: #11CDEF; /* Cyan */
        --color-accent-dark: #0B8BAD; /* Darker Cyan for text on light accent bg */
        --color-neutral: #232759; /* Dark Blue-Gray */
        --color-base-100: #FFFFFF; /* White */
        --color-neutral-70: #8898AA; /* Secondary Text */
        --color-neutral-30: #DEE4EC; /* Lightest Neutral, Borders */

        /* Standard Semantic Colors (Redefined/Extended for AI context) */
        --color-status-info: #1171EF; /* Blue - Neutral AI findings, general info */
        --color-status-success: #2DCE89; /* Green - Success, positive AI insights, low-priority opportunities */
        --color-status-warning: #FB6340; /* Orange - Warning, medium risk */
        --color-status-error: #F5365C; /* Red - Error, high risk, conflicts */
        --color-status-pending: #FFD700; /* Yellow/Gold - Pending analysis */
        
        /* AI-Specific Functional Colors */
        --color-ai-opportunity-high: #4CAF50; /* Vibrant Green */
        --color-ai-opportunity-medium: #8BC34A; /* Lighter Green */
        --color-ai-risk-high: #D32F2F; /* Dark Red */
        --color-ai-risk-medium: #EF5350; /* Lighter Red */
        --color-ai-next-step: #673AB7; /* Purple */
        --color-ai-synergy: #00BCD4; /* Turquoise/Teal */
        --color-ai-creative: #FFC107; /* Amber */
        --color-ai-technical: #2196F3; /* Rich Blue */
        --color-ai-research: #9C27B0; /* Dark Purple */
        --color-ai-developer-prompt: #4DD0E1; /* Light Turquoise */

        /* Code styling colors */
        --color-code-bg: #1a1a1a; /* Dark background for code blocks */
        --color-code-text: #10b981; /* Green text for code */
        --color-code-inline-bg: #2d2d2d; /* Inline code background */
        --color-code-border: #404040; /* Code border */
        --color-code-lang: #60a5fa; /* Language label color */

        /* General Info, Success, Warning, Error for non-status specific contexts (if needed to differentiate) */
        --color-info: #007BFF; 
        --color-success: #28A745; 
        --color-warning: #FFC107; 
        --color-error: #DC3545;

        /* Layout Variables */
        --header-height: 4rem; /* 64px */
        --sidenav-width-desktop: 16rem; /* 256px for w-64 */
        --sidenav-width-large-desktop: 18rem; /* 288px for lg:w-72 */

         /* React Flow specific colors (derived from theme) */
        --color-rf-node-entity-bg: var(--color-ai-technical);
        --color-rf-node-entity-text: var(--color-base-100);
        --color-rf-node-concept-bg: var(--color-ai-research);
        --color-rf-node-concept-text: var(--color-base-100);
        --color-rf-edge: var(--color-primary);
        --color-rf-edge-label-bg: var(--color-base-100);
        --color-rf-edge-label-text: var(--color-neutral);
        --color-rf-controls-bg: var(--color-base-100);
        --color-rf-controls-button-bg: var(--color-secondary);
        --color-rf-controls-border: var(--color-neutral-30);
        --color-rf-minimap-mask: rgba(0,0,0,0.05);
        --color-rf-background: var(--color-secondary);
        --color-rf-grid: var(--color-neutral-30);
      }
      body {
        font-family: 'Inter', sans-serif;
        background-color: var(--color-secondary);
        color: var(--color-neutral);
      }

      .hero { fill: currentColor; width: 1.25em; height: 1.25em; display: inline-block; vertical-align: middle; }
      
      input[type="text"],
      input[type="search"],
      input[type="email"],
      input[type="password"],
      textarea,
      select {
        transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        background-color: var(--color-base-100);
        border: 1px solid var(--color-neutral-30);
      }

      input[type="text"]:focus,
      input[type="search"]:focus,
      input[type="email"]:focus,
      input[type="password"]:focus,
      textarea:focus,
      select:focus {
        border-color: var(--color-primary) !important;
        box-shadow: 0 0 0 3px var(--color-primary / 0.2) !important; 
        outline: 2px solid transparent !important;
      }
      
      .animate-linear-progress-bar-element {
        animation: linearProgressBar 1.5s ease-in-out infinite;
      }
      
      @media (min-width: 768px) { 
        .sticky-save-button-md {
            left: calc(var(--sidenav-width-desktop) + 2rem); 
            bottom: 1.5rem; 
        }
      }
      @media (min-width: 1024px) { 
         .sticky-save-button-lg {
            left: calc(var(--sidenav-width-large-desktop) + 2.5rem); 
            bottom: 1.5rem; 
        }
      }
      @keyframes toastIn {
        from { opacity: 0; transform: translateX(100%); }
        to { opacity: 1; transform: translateX(0); }
      }
      .animate-toast-in {
        animation: toastIn 0.3s ease-out forwards;
      }
      .custom-scrollbar::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      .custom-scrollbar::-webkit-scrollbar-track {
        background: var(--color-secondary);
        border-radius: 10px;
      }
      .custom-scrollbar::-webkit-scrollbar-thumb {
        background: var(--color-neutral-30);
        border-radius: 10px;
      }
      .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: var(--color-neutral-70);
      }

      /* React Flow Customizations to use CSS Variables */
      /* Removed specific .react-flow__node and .react-flow__node-default overrides for padding/font-size
         as these are now handled by the JavaScript styling in KnowledgeGraphVisualizerInternal.tsx. */
      
      .react-flow__edge-textbg {
        fill: var(--color-rf-edge-label-bg) !important;
      }
      .react-flow__edge-text {
        fill: var(--color-rf-edge-label-text) !important;
        font-size: 9px; /* Removed !important, as JS style also sets this */
      }
      .react-flow__controls {
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
      }
      .react-flow__controls-button {
        background-color: var(--color-rf-controls-button-bg) !important;
        border-bottom: 1px solid var(--color-rf-controls-border) !important;
        fill: var(--color-neutral) !important;
      }
      .react-flow__controls-button:hover {
         background-color: var(--color-neutral-30) !important;
      }
      .react-flow__minimap {
        background-color: var(--color-base-100) !important;
        border: 1px solid var(--color-neutral-30);
      }
      .react-flow__minimap-mask {
        fill: var(--color-rf-minimap-mask) !important;
      }
       .react-flow__background {
        background-color: var(--color-rf-background) !important;
      }
    </style>
  <script type="importmap">
{
  "imports": {
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.1",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.2.0",
    "uuid": "https://esm.sh/uuid@^11.1.0",
    "i18next": "https://esm.sh/i18next@^23.11.5",
    "react-i18next": "https://esm.sh/react-i18next@^14.1.2",
    "i18next-browser-languagedetector": "https://esm.sh/i18next-browser-languagedetector@^8.0.0",
    "@tailwindcss/typography": "https://esm.sh/@tailwindcss/typography@^0.5.10",
    "jszip": "https://esm.sh/jszip@3.10.1",
    "@/src/locales/en/translation.json": "/src/locales/en/translation.json",
    "@/": "/",
    "i18": "https://esm.sh/i18@^0.0.6",
    "reactflow": "https://esm.sh/reactflow@11.11.3"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>