
import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Project, AISuggestion } from '../types';
import { ICONS } from '../config'; // Updated import
import { useProjects } from '../hooks/useProjects';

interface ProjectCardProps {
  project: Project;
  onShowQuickView: (project: Project) => void;
  portfolioInteraction?: 'synergy' | 'conflict' | 'sharedResource' | null;
}

const ProjectCard: React.FC<ProjectCardProps> = ({ project, onShowQuickView, portfolioInteraction }) => {
  const { t } = useTranslation();
  const { initialAnalysis, strategicOutput } = project;
  const { toggleFavoriteProject } = useProjects();

  const getStatusColorInfo = () => {
    switch (project.status) {
      case 'analyzing_initial':
      case 'analyzing_deep':
        return { barClass: 'bg-status-pending', textClass: 'text-neutral-700 font-semibold' };
      case 'analyzed_initial':
      case 'analyzed_deep':
        return { barClass: 'bg-status-success', textClass: 'text-base-100' };
      case 'error':
        return { barClass: 'bg-status-error', textClass: 'text-base-100' };
      default: // 'new'
        return { barClass: 'bg-neutral-30', textClass: 'text-neutral-700' };
    }
  };

  const getStatusText = () => {
    // Construct the key dynamically, e.g., projectCard.statusNew, projectCard.statusAnalyzingInitial
    const statusKey = `projectCard.status${project.status.charAt(0).toUpperCase() + project.status.slice(1).replace(/_([a-z])/g, (g) => g[1].toUpperCase())}`;
    return t(statusKey as any, { defaultValue: project.status }); // Cast to any to satisfy i18next key type if using strict typing
  }


  const handleFavoriteToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    toggleFavoriteProject(project.id);
  };

  const handleQuickViewClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onShowQuickView(project);
  };

  let cardBorderClass = "border-transparent";
  let highlightRingClass = "";
  let portfolioInteractionText = "";

  if (portfolioInteraction) {
    switch (portfolioInteraction) {
        case 'synergy':
            cardBorderClass = "border-status-success";
            highlightRingClass = "ring-2 ring-status-success ring-offset-2";
            portfolioInteractionText = t('dashboard.aiPortfolioOverview.synergies');
            break;
        case 'conflict':
            cardBorderClass = "border-status-error";
            highlightRingClass = "ring-2 ring-status-error ring-offset-2";
            portfolioInteractionText = t('dashboard.aiPortfolioOverview.conflicts');
            break;
        case 'sharedResource':
            cardBorderClass = "border-ai-research"; // Using ai-research for shared resources
            highlightRingClass = "ring-2 ring-ai-research ring-offset-2";
            portfolioInteractionText = t('dashboard.aiPortfolioOverview.sharedResources');
            break;
    }
  }


  const getAITypeColor = (typeGuess: string = "") => {
    const lowerType = typeGuess.toLowerCase();
    if (lowerType.includes(t('keywords.software', {lng: 'en'}).toLowerCase()) || lowerType.includes(t('keywords.develop', {lng: 'en'}).toLowerCase()) || lowerType.includes(t('keywords.app', {lng: 'en'}).toLowerCase())) return "bg-ai-technical/20 text-ai-technical border border-ai-technical/30";
    if (lowerType.includes(t('keywords.creative', {lng: 'en'}).toLowerCase()) || lowerType.includes(t('keywords.design', {lng: 'en'}).toLowerCase()) || lowerType.includes(t('keywords.market', {lng: 'en'}).toLowerCase())) return "bg-ai-creative/20 text-ai-creative border border-ai-creative/30";
    if (lowerType.includes(t('keywords.research', {lng: 'en'}).toLowerCase()) || lowerType.includes(t('keywords.analysis', {lng: 'en'}).toLowerCase())) return "bg-ai-research/20 text-ai-research border border-ai-research/30";
    return "bg-secondary text-neutral-70 border border-neutral-300";
  }

  let summaryHighlightClass = "text-neutral-70";
  if (initialAnalysis?.summary) {
    const summaryLower = initialAnalysis.summary.toLowerCase();
    // Using English for keyword matching as AI likely generates English keywords for internal logic,
    // even if summary is translated. For more robustness, AI could also return standardized risk/opportunity flags.
    const highRiskKeywords = ["critical risk", "high risk", "significant danger", "major threat"];
    const highOpportunityKeywords = ["major opportunity", "significant potential", "high potential", "unique advantage"];

    if (highRiskKeywords.some(kw => summaryLower.includes(kw))) {
        summaryHighlightClass = "text-ai-risk-high font-semibold";
    } else if (highOpportunityKeywords.some(kw => summaryLower.includes(kw))) {
        summaryHighlightClass = "text-ai-opportunity-high font-semibold";
    }
  }


  return (
    <div className={`relative group bg-base-100 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 ease-in-out transform hover:-translate-y-1 h-full flex flex-col border-2 ${cardBorderClass} ${highlightRingClass}`}>
      <div className={`p-1.5 text-center text-xs font-semibold ${getStatusColorInfo().barClass} ${getStatusColorInfo().textClass} flex items-center justify-center relative overflow-hidden h-7 rounded-t-md`}>
        { (project.status === 'analyzing_initial' || project.status === 'analyzing_deep') && (
            <span className="absolute left-2 w-2 h-2 bg-base-100/70 rounded-full animate-pulse-fast"></span>
        )}
        {getStatusText()}
        {portfolioInteractionText && <span className="absolute right-2 text-xs italic opacity-80">({portfolioInteractionText})</span>}
      </div>

      <button
        onClick={handleFavoriteToggle}
        className="absolute top-1 right-1 p-1.5 text-neutral-500 hover:text-yellow-500 transition-colors z-10 rounded-full hover:bg-neutral-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-yellow-500"
        aria-label={project.isFavorite ? t('projectCard.unmarkAsFavorite') : t('projectCard.markAsFavorite')}
        title={project.isFavorite ? t('projectCard.unmarkAsFavorite') : t('projectCard.markAsFavorite')}
      >
        {project.isFavorite ? React.cloneElement(ICONS.STAR_SOLID, { className: "w-5 h-5 text-yellow-400" }) : ICONS.STAR_OUTLINE}
      </button>

      <Link to={`/project/${project.id}`} className="flex flex-col flex-grow p-4">
        <div className="flex-grow">
          <div className="flex items-start justify-between mb-1.5">
            <h3 className="text-md font-semibold text-primary flex items-center">
              {React.cloneElement(ICONS.PROJECT, {className: "w-5 h-5 mr-2 flex-shrink-0"})} <span className="line-clamp-2">{project.name}</span>
            </h3>
          </div>

          {initialAnalysis?.summary && (
            <p className={`text-xs ${summaryHighlightClass} mb-2 line-clamp-2 h-8`}>{initialAnalysis.summary}</p>
          )}
          {!initialAnalysis?.summary && (
             <p className="text-xs text-neutral-70 mb-2 h-8 line-clamp-2">{project.description}</p>
          )}

          {initialAnalysis && initialAnalysis.keywords.length > 0 && (
            <div className="mb-3">
              <div className="flex flex-wrap gap-1.5">
                {initialAnalysis.keywords.slice(0, 3).map(keyword => {
                  let keywordClass = "bg-secondary text-neutral-70 border border-neutral-300";
                  const kwLower = keyword.toLowerCase();

                  if (strategicOutput?.strategicSuggestions?.some(sugg => sugg.type === 'risk' && sugg.priority === 'high' && (sugg.title.toLowerCase().includes(kwLower) || sugg.description.toLowerCase().includes(kwLower)) )) {
                    keywordClass = "bg-ai-risk-high/10 text-ai-risk-high border border-ai-risk-high/30";
                  } else if (strategicOutput?.strategicSuggestions?.some(sugg => sugg.type === 'opportunity' && sugg.priority === 'high' && (sugg.title.toLowerCase().includes(kwLower) || sugg.description.toLowerCase().includes(kwLower)) )) {
                    keywordClass = "bg-ai-opportunity-high/10 text-ai-opportunity-high border border-ai-opportunity-high/30";
                  } else if (strategicOutput?.strategicSuggestions?.some(sugg => sugg.type === 'risk' && (sugg.title.toLowerCase().includes(kwLower) || sugg.description.toLowerCase().includes(kwLower)) )) {
                    keywordClass = "bg-ai-risk-medium/10 text-ai-risk-medium border border-ai-risk-medium/30";
                  } else if (strategicOutput?.strategicSuggestions?.some(sugg => sugg.type === 'opportunity' && (sugg.title.toLowerCase().includes(kwLower) || sugg.description.toLowerCase().includes(kwLower)) )) {
                     keywordClass = "bg-ai-opportunity-medium/10 text-ai-opportunity-medium border border-ai-opportunity-medium/30";
                  }


                  return (
                    <span key={keyword} className={`px-2 py-0.5 ${keywordClass} text-xs rounded-full font-medium`}>
                      {keyword}
                    </span>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {initialAnalysis?.projectTypeGuess && (
            <div className={`mt-auto text-xs font-semibold px-2.5 py-1 rounded-md self-start ${getAITypeColor(initialAnalysis.projectTypeGuess)}`}>
                {initialAnalysis.projectTypeGuess}
            </div>
        )}
      </Link>

      <div className="p-3 bg-secondary/30 border-t border-neutral-200/50 flex justify-between items-center mt-auto rounded-b-md">
        <button
          onClick={handleQuickViewClick}
          className="text-xs text-primary hover:underline flex items-center focus:outline-none focus-visible:ring-1 focus-visible:ring-primary rounded px-1 py-0.5"
          aria-label={t('projectCard.quickViewAriaLabel', { projectName: project.name })}
        >
          {React.cloneElement(ICONS.EYE, {className: "w-4 h-4 mr-1"})}
          {t('projectCard.quickView')}
        </button>
        <Link to={`/project/${project.id}`} className="text-xs text-primary hover:underline focus:outline-none focus-visible:ring-1 focus-visible:ring-primary rounded px-1 py-0.5">
            {t('projectCard.viewDetails')} &rarr;
        </Link>
      </div>
    </div>
  );
};

export default ProjectCard;
