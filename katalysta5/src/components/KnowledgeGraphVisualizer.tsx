
import React, { useLayoutEffect } from 'react';
import { useTranslation } from 'react-i18next';
import ReactFlow, {
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  MiniMap,
  Node,
  Edge,
  Position,
  MarkerType,
  ReactFlowProvider
} from 'reactflow';
// Note: React Flow CSS is imported globally in index.html via a <link> tag or @import in <style>
// If it wasn't, it would be: import 'reactflow/dist/style.css';


import { KnowledgeGraph, KnowledgeGraphElement, KnowledgeGraphRelation } from '../types';
import { ICONS } from '../config';

interface KnowledgeGraphVisualizerProps {
  graph?: KnowledgeGraph;
}

const nodeWidth = 160; 
const nodeHeight = 40; 

// This is the internal component that uses React Flow hooks
const KnowledgeGraphVisualizerInternal: React.FC<KnowledgeGraphVisualizerProps> = ({ graph }) => {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const { t } = useTranslation();

  useLayoutEffect(() => {
    if (graph?.elements && graph.relations) {
      const validNodes: Node[] = graph.elements
        .filter(el => el && typeof el.id === 'string' && el.id.trim() !== '' && typeof el.label === 'string' && el.label.trim() !== '')
        .map((el: KnowledgeGraphElement, index: number) => {
          const isEntity = el.type === 'entity';
          return {
            id: el.id,
            data: { label: el.label },
            position: { 
              x: (index % 3) * (nodeWidth + 70), 
              y: Math.floor(index / 3) * (nodeHeight + 60) 
            },
            type: 'default',
            style: {
              background: isEntity ? 'var(--color-rf-node-entity-bg)' : 'var(--color-rf-node-concept-bg)',
              color: isEntity ? 'var(--color-rf-node-entity-text)' : 'var(--color-rf-node-concept-text)',
              border: `1px solid ${isEntity ? 'var(--color-ai-technical)' : 'var(--color-ai-research)'}`,
              borderRadius: '6px',
              width: nodeWidth,
              minHeight: nodeHeight,
              padding: '5px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '10px',
              lineHeight: '1.2',
              textAlign: 'center',
              boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
            },
            sourcePosition: Position.Right,
            targetPosition: Position.Left,
          };
        });

      const validEdges: Edge[] = graph.relations
        .filter(rel => rel && 
                       typeof rel.source === 'string' && rel.source.trim() !== '' &&
                       typeof rel.target === 'string' && rel.target.trim() !== '' &&
                       typeof rel.label === 'string' && rel.label.trim() !== '' &&
                       // Ensure source and target nodes exist in the validNodes list
                       validNodes.some(node => node.id === rel.source) &&
                       validNodes.some(node => node.id === rel.target)
        )
        .map((rel: KnowledgeGraphRelation, index: number) => ({
          id: `edge-${rel.source}-${rel.target}-${rel.label.replace(/\s+/g, '-')}-${index}`,
          source: rel.source,
          target: rel.target,
          label: rel.label,
          type: 'smoothstep',
          markerEnd: {
            type: MarkerType.ArrowClosed,
            width: 15,
            height: 15,
            color: 'var(--color-rf-edge)',
          },
          style: {
            strokeWidth: 1.5,
            stroke: 'var(--color-rf-edge)',
          },
          labelStyle: { 
              fill: 'var(--color-rf-edge-label-text)', 
              fontSize: 9,
              fontWeight: 600 
          },
          labelBgStyle: { 
              fill: 'var(--color-rf-edge-label-bg)', 
              fillOpacity: 0.7 
          },
          labelBgPadding: [3, 2] as [number, number],
          labelBgBorderRadius: 2,
        }));

      setNodes(validNodes);
      setEdges(validEdges);
    } else {
      // Clear nodes and edges if graph data is incomplete or missing
      setNodes([]);
      setEdges([]);
    }
  }, [graph, setNodes, setEdges]);

  // Scenario 1: AI provided elements, but ALL were invalid after filtering.
  if (nodes.length === 0 && graph?.elements && graph.elements.length > 0) {
    return (
      <div style={{ padding: '20px', textAlign: 'center', color: 'var(--color-neutral-70)', height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <p>{t('knowledgeGraph.noValidElementsToDisplay')}</p>
      </div>
    );
  }

  // Scenario 2: No nodes to render for ReactFlow (either originally empty elements from AI, or some other edge case resulting in empty nodes).
  // This is a more general check after the specific "all elements were invalid" check.
  if (nodes.length === 0) {
    // This component is rendered if graph.summary or graph.relations exist, even if graph.elements is empty.
    // So, if nodes state is empty, it's because no graph nodes can be displayed.
    return (
      <div style={{ padding: '20px', textAlign: 'center', color: 'var(--color-neutral-70)', height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <p>{t('knowledgeGraph.noElementsToDisplay')}</p>
      </div>
    );
  }

  return (
    <div style={{ height: 500, width: '100%', border: '1px solid var(--color-neutral-30)', borderRadius: '8px', background: 'var(--color-rf-background)' }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        fitView
        nodesDraggable
        panOnDrag
        zoomOnScroll
        zoomOnPinch
        zoomOnDoubleClick
        attributionPosition="bottom-right"
      >
        <Controls 
            style={{ 
                bottom: 10, 
                left: 10,
                boxShadow: 'none', 
                border: '1px solid var(--color-rf-controls-border)'
            }} 
        />
        <MiniMap 
            nodeStrokeWidth={3} 
            zoomable 
            pannable 
            style={{
                bottom: 10,
                right: 10,
                height: 80,
                border: '1px solid var(--color-rf-controls-border)'
            }}
        />
        <Background gap={16} color={'var(--color-rf-grid)'} variant={'dots'} />
      </ReactFlow>
    </div>
  );
};

// This is the main exported component which includes the ReactFlowProvider
const KnowledgeGraphVisualizer: React.FC<KnowledgeGraphVisualizerProps> = (props) => {
  const { t } = useTranslation();

  if (!props.graph || (!props.graph.elements?.length && !props.graph.relations?.length && !props.graph.summary)) {
    return <p className="text-neutral-500 text-sm">{t('knowledgeGraph.notAvailable')}</p>;
  }
  
  // Check if there's any data at all to attempt rendering.
  // The Internal component will handle more specific "no valid data" or "no elements" messages.
  const hasAnyDataToAttemptRender = 
    (props.graph.elements && props.graph.elements.length > 0) || 
    (props.graph.relations && props.graph.relations.length > 0) ||
    (props.graph.summary && props.graph.summary.trim() !== ''); // Render even if only summary exists

  return (
    <div className="space-y-4">
      {props.graph.summary && (
        <div className="p-3 bg-primary/5 rounded-md border border-primary/10 flex items-start">
          {ICONS.AI_SPARKLE && React.cloneElement(ICONS.AI_SPARKLE, {className: "w-4 h-4 mr-2 mt-0.5 text-primary flex-shrink-0"})}
          <p className="text-sm text-neutral-70 italic">
            <strong className="not-italic text-primary">{t('knowledgeGraph.aiSummary')}</strong> {props.graph.summary}
          </p>
        </div>
      )}
      
      {hasAnyDataToAttemptRender ? (
        <>
          {(props.graph.elements && props.graph.elements.length > 0) && ( // Only show this note if elements are expected
            <p className="text-xs text-neutral-500 mb-2">
                {t('knowledgeGraph.interactiveNote')}
                {' '}{t('knowledgeGraph.dagreRecommendation')}
            </p>
          )}
          <ReactFlowProvider>
            <KnowledgeGraphVisualizerInternal {...props} />
          </ReactFlowProvider>
        </>
      ) : (
        // This case might not be hit if summary alone triggers rendering of Internal,
        // but kept as a fallback for truly empty graph objects from AI.
        <p className="text-neutral-500 text-sm mt-2">{t('knowledgeGraph.noElementsToDisplay')}</p>
      )}
      
      {(props.graph.elements && props.graph.elements.length > 0) && ( // Only show this note if elements are expected
        <p className="text-xs text-neutral-500 mt-4">
            {t('knowledgeGraph.interactiveGraphNote')}
        </p>
      )}
    </div>
  );
};

export default KnowledgeGraphVisualizer;
