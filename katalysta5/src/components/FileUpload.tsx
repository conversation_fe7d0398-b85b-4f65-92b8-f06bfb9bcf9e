
import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { ProjectFile } from '../types';
import { ICONS, FILE_TYPES_SUPPORTED } from '../config'; // Updated import
import { v4 as uuidv4 } from 'uuid';

interface FileUploadProps {
  onFilesUploaded: (files: ProjectFile[]) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ onFilesUploaded }) => {
  const { t } = useTranslation();
  const [selectedFiles, setSelectedFiles] = useState<ProjectFile[]>([]);

  const handleSimulatedFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const newProjectFiles: ProjectFile[] = files.map(file => ({
      id: uuidv4(),
      name: file.name,
      type: file.type || 'unknown',
      size: file.size,
    }));
    
    if (newProjectFiles.length === 0 && selectedFiles.length === 0) {
        const conceptualFiles: ProjectFile[] = [
            { id: uuidv4(), name: 'Project_Brief.pdf', type: 'application/pdf', size: 1024 * 250 },
            { id: uuidv4(), name: 'Initial_Data.json', type: 'application/json', size: 1024 * 50 },
            { id: uuidv4(), name: 'User_Research_Notes.txt', type: 'text/plain', size: 1024 * 15 },
        ];
        setSelectedFiles(conceptualFiles);
        onFilesUploaded(conceptualFiles);
    } else {
        const allFiles = [...selectedFiles, ...newProjectFiles];
        setSelectedFiles(allFiles);
        onFilesUploaded(allFiles);
    }
    // Clear the input value to allow selecting the same file again if removed and re-added
    if (event.target) {
        event.target.value = '';
    }
  }, [onFilesUploaded, selectedFiles]);

  const removeFile = (fileId: string) => {
    const updatedFiles = selectedFiles.filter(f => f.id !== fileId);
    setSelectedFiles(updatedFiles);
    onFilesUploaded(updatedFiles);
  };

  return (
    <div className="space-y-3">
      <div className="relative border-2 border-dashed border-neutral-30 rounded-md p-6 text-center hover:border-primary transition-colors">
        {ICONS.UPLOAD && React.cloneElement(ICONS.UPLOAD, { className: "w-8 h-8 mx-auto text-neutral-400"})}
        <p className="mt-2 text-sm text-neutral-70">
          {t('fileUpload.dragOrClick')}
        </p>
        <p className="text-xs text-neutral-50">{t('fileUpload.conceptualUploader')}</p>
        <input 
            type="file" 
            multiple 
            onChange={handleSimulatedFileSelect}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            aria-label={t('fileUpload.dragOrClick')}
            title={t('fileUpload.dragOrClick')}
        />
      </div>
      {selectedFiles.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-neutral-80">{t('fileUpload.selectedFiles')}</h4>
          <ul className="mt-2 space-y-1 text-sm text-neutral-70 max-h-32 overflow-y-auto p-2 bg-secondary rounded-md border border-neutral-200">
            {selectedFiles.map(file => (
              <li key={file.id} className="flex justify-between items-center p-1.5 bg-base-100 rounded shadow-sm">
                <span className="truncate pr-2">{file.name} ({Math.round(file.size / 1024)} KB)</span>
                <button 
                    type="button" 
                    onClick={() => removeFile(file.id)} 
                    className="text-error hover:text-error/70 text-xs font-semibold px-2 py-0.5 rounded hover:bg-error/10 transition-colors"
                    aria-label={t('fileUpload.removeAriaLabel', { fileName: file.name })}
                >
                  {t('fileUpload.remove')}
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
      <p className="text-xs text-neutral-50">
        {t('fileUpload.demoHint')}
      </p>
    </div>
  );
};

export default FileUpload;
