
import React from 'react';
import { useTranslation } from 'react-i18next';
import { ICONS } from '../config'; // Updated import, though ICONS not used directly here

interface FilterSortPanelProps {
  filters: {
    status: string;
    favorite: 'all' | 'yes' | 'no';
    searchTerm: string;
  };
  onFilterChange: (filterName: keyof FilterSortPanelProps['filters'], value: string) => void;
  sortBy: string;
  onSortChange: (sortKey: string) => void;
  projectStatuses: string[]; // e.g., ['new', 'analyzing_initial', ...]
}

const ProjectFilterPanel: React.FC<FilterSortPanelProps> = ({
  filters,
  onFilterChange,
  sortBy,
  onSortChange,
  projectStatuses
}) => {
  const { t } = useTranslation();

  const sortOptions = [
    { key: 'createdAt_desc', labelKey: 'filterSortPanel.sortBy.newestFirst' },
    { key: 'createdAt_asc', labelKey: 'filterSortPanel.sortBy.oldestFirst' },
    { key: 'name_asc', labelKey: 'filterSortPanel.sortBy.nameAsc' },
    { key: 'name_desc', labelKey: 'filterSortPanel.sortBy.nameDesc' },
    { key: 'updatedAt_desc', labelKey: 'filterSortPanel.sortBy.recentlyUpdated' },
  ];

  return (
    <div className="p-4 bg-base-100 rounded-lg shadow-md mb-6 space-y-4 md:space-y-0 md:flex md:flex-wrap md:items-end md:justify-between gap-4">
      {/* Search Term */}
      <div className="flex-grow min-w-[200px]">
        <label htmlFor="searchTerm" className="block text-xs font-medium text-neutral/70 mb-1">
          {t('filterSortPanel.searchByName')}
        </label>
        <input
          type="text"
          id="searchTerm"
          placeholder={t('filterSortPanel.searchPlaceholder')}
          value={filters.searchTerm}
          onChange={(e) => onFilterChange('searchTerm', e.target.value)}
          className="w-full px-3 py-1.5 border border-neutral/30 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm text-xs"
        />
      </div>

      {/* Status Filter */}
      <div className="min-w-[150px]">
        <label htmlFor="statusFilter" className="block text-xs font-medium text-neutral/70 mb-1">
          {t('filterSortPanel.filterByStatus')}
        </label>
        <select
          id="statusFilter"
          value={filters.status}
          onChange={(e) => onFilterChange('status', e.target.value)}
          className="w-full px-3 py-1.5 border border-neutral/30 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm text-xs bg-base-100"
        >
          <option value="">{t('filterSortPanel.allStatuses')}</option>
          {projectStatuses.map(status => (
            <option key={status} value={status}>{t(`projectCard.status${status.charAt(0).toUpperCase() + status.slice(1).replace(/_([a-z])/g, (g) => g[1].toUpperCase())}` as any, status)}</option>
          ))}
        </select>
      </div>

      {/* Favorite Filter */}
      <div className="min-w-[120px]">
        <label htmlFor="favoriteFilter" className="block text-xs font-medium text-neutral/70 mb-1">
          {t('filterSortPanel.filterByFavorite')}
        </label>
        <select
          id="favoriteFilter"
          value={filters.favorite}
          onChange={(e) => onFilterChange('favorite', e.target.value)}
          className="w-full px-3 py-1.5 border border-neutral/30 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm text-xs bg-base-100"
        >
          <option value="all">{t('filterSortPanel.all')}</option>
          <option value="yes">{t('filterSortPanel.yes')}</option>
          <option value="no">{t('filterSortPanel.no')}</option>
        </select>
      </div>
      
      {/* Sort By */}
      <div className="min-w-[180px]">
        <label htmlFor="sortBy" className="block text-xs font-medium text-neutral/70 mb-1">
          {t('filterSortPanel.sortBy.label')}
        </label>
        <select
          id="sortBy"
          value={sortBy}
          onChange={(e) => onSortChange(e.target.value)}
          className="w-full px-3 py-1.5 border border-neutral/30 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm text-xs bg-base-100"
        >
          {sortOptions.map(opt => (
            <option key={opt.key} value={opt.key}>{t(opt.labelKey)}</option>
          ))}
        </select>
      </div>
    </div>
  );
};

export default ProjectFilterPanel;
