
import React from 'react';

interface MarkdownRendererProps {
  markdown: string;
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ markdown }) => {

  // Simple syntax highlighting for common languages
  const highlightCode = (code: string, language: string): JSX.Element[] => {
    const lines = code.split('\n');
    return lines.map((line, index) => {
      if (line.trim() === '') return <div key={index}><br /></div>;

      // Basic syntax highlighting patterns
      let highlightedLine = line;

      // Keywords for different languages
      const keywords = {
        javascript: ['function', 'const', 'let', 'var', 'if', 'else', 'for', 'while', 'return', 'class', 'import', 'export', 'async', 'await'],
        typescript: ['function', 'const', 'let', 'var', 'if', 'else', 'for', 'while', 'return', 'class', 'import', 'export', 'async', 'await', 'interface', 'type'],
        python: ['def', 'class', 'if', 'else', 'elif', 'for', 'while', 'return', 'import', 'from', 'try', 'except', 'with'],
        java: ['public', 'private', 'protected', 'class', 'interface', 'if', 'else', 'for', 'while', 'return', 'import', 'package'],
        css: ['color', 'background', 'margin', 'padding', 'border', 'width', 'height', 'display', 'position', 'font'],
      };

      const langKeywords = keywords[language.toLowerCase()] || [];

      // Apply basic highlighting
      const parts: (string | JSX.Element)[] = [];
      let lastIndex = 0;

      // Highlight strings
      const stringRegex = /(['"`])((?:\\.|(?!\1)[^\\])*?)\1/g;
      let match;
      const matches: Array<{start: number, end: number, type: string, content: string}> = [];

      while ((match = stringRegex.exec(line)) !== null) {
        matches.push({start: match.index, end: match.index + match[0].length, type: 'string', content: match[0]});
      }

      // Highlight comments
      if (line.trim().startsWith('//') || line.trim().startsWith('#')) {
        return <div key={index} style={{color: '#6b7280'}}>{line}</div>;
      }

      // Highlight keywords
      langKeywords.forEach(keyword => {
        const keywordRegex = new RegExp(`\\b${keyword}\\b`, 'g');
        let keywordMatch;
        while ((keywordMatch = keywordRegex.exec(line)) !== null) {
          matches.push({start: keywordMatch.index, end: keywordMatch.index + keyword.length, type: 'keyword', content: keyword});
        }
      });

      // Sort matches by position
      matches.sort((a, b) => a.start - b.start);

      // Build highlighted line
      matches.forEach((match, i) => {
        if (match.start > lastIndex) {
          parts.push(line.substring(lastIndex, match.start));
        }

        if (match.type === 'keyword') {
          parts.push(<span key={`${index}-${i}`} style={{color: '#8b5cf6', fontWeight: 'bold'}}>{match.content}</span>);
        } else if (match.type === 'string') {
          parts.push(<span key={`${index}-${i}`} style={{color: '#10b981'}}>{match.content}</span>);
        }

        lastIndex = match.end;
      });

      if (lastIndex < line.length) {
        parts.push(line.substring(lastIndex));
      }

      return <div key={index}>{parts.length > 0 ? parts : line}</div>;
    });
  };

  const renderMarkdown = (md: string): JSX.Element[] => {
    const lines = md.split('\n');
    const elements: JSX.Element[] = [];
    let currentListType: 'ul' | 'ol' | null = null;
    let listItems: JSX.Element[] = [];

    const closeList = () => {
      if (currentListType && listItems.length > 0) {
        if (currentListType === 'ul') {
          elements.push(<ul key={`ul-${elements.length}`} className="list-disc pl-5 mb-4">{listItems}</ul>);
        } else {
          elements.push(<ol key={`ol-${elements.length}`} className="list-decimal pl-5 mb-4">{listItems}</ol>);
        }
      }
      listItems = [];
      currentListType = null;
    };

    const processInlineFormatting = (text: string, keyPrefix: string): (string | JSX.Element)[] => {
      const parts: (string | JSX.Element)[] = [];
      let lastIndex = 0;
      // Regex for images, links, bold, italic, and inline code
      // Order matters: process images first, then links, then other formatting.
      const inlineRegex = /(!\[(.*?)\]\((.*?)\)|\[(.*?)\]\((.*?)\)|\*\*(?!\s)(.*?)(?<!\s)\*\*|\*(?!\s)(.*?)(?<!\s)\*|`(.*?)`)/g;

      let match;
      while ((match = inlineRegex.exec(text)) !== null) {
        if (match.index > lastIndex) {
          parts.push(text.substring(lastIndex, match.index));
        }
        if (match[1].startsWith('![')) { // Image: ![alt](src)
          parts.push(<img key={`${keyPrefix}-img-${match.index}`} src={match[3]} alt={match[2]} className="max-w-full h-auto rounded-md my-2 shadow-sm" />);
        } else if (match[1].startsWith('[')) { // Link: [text](url)
          parts.push(<a key={`${keyPrefix}-link-${match.index}`} href={match[5]} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">{match[4]}</a>);
        } else if (match[1].startsWith('**')) { // Bold **text**
          parts.push(<strong key={`${keyPrefix}-strong-${match.index}`}>{match[6]}</strong>);
        } else if (match[1].startsWith('*')) { // Italic *text*
          parts.push(<em key={`${keyPrefix}-em-${match.index}`}>{match[7]}</em>);
        } else if (match[1].startsWith('`')) { // Inline code `text`
          parts.push(<code key={`${keyPrefix}-code-${match.index}`} className="inline-code px-2 py-1 rounded text-sm font-mono font-semibold" style={{backgroundColor: 'var(--color-code-inline-bg)', color: 'var(--color-code-text)', border: '1px solid var(--color-code-border)'}}>{match[8]}</code>);
        }
        lastIndex = inlineRegex.lastIndex;
      }
      if (lastIndex < text.length) {
        parts.push(text.substring(lastIndex));
      }
      return parts;
    };


    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineKeyPrefix = `line-${i}`;

      // Headings
      if (line.startsWith('# ')) { closeList(); elements.push(<h1 key={`${lineKeyPrefix}-h1`} className="text-3xl font-bold mt-6 mb-3 border-b pb-2">{processInlineFormatting(line.substring(2), `${lineKeyPrefix}-h1-content`)}</h1>); continue; }
      if (line.startsWith('## ')) { closeList(); elements.push(<h2 key={`${lineKeyPrefix}-h2`} className="text-2xl font-semibold mt-5 mb-2 border-b pb-1">{processInlineFormatting(line.substring(3), `${lineKeyPrefix}-h2-content`)}</h2>); continue; }
      if (line.startsWith('### ')) { closeList(); elements.push(<h3 key={`${lineKeyPrefix}-h3`} className="text-xl font-semibold mt-4 mb-2">{processInlineFormatting(line.substring(4), `${lineKeyPrefix}-h3-content`)}</h3>); continue; }
      if (line.startsWith('#### ')) { closeList(); elements.push(<h4 key={`${lineKeyPrefix}-h4`} className="text-lg font-semibold mt-3 mb-1">{processInlineFormatting(line.substring(5), `${lineKeyPrefix}-h4-content`)}</h4>); continue; }
      
      // Horizontal Rule
      if (line.match(/^(\*\*\*|---|___)\s*$/)) {
        closeList();
        elements.push(<hr key={`${lineKeyPrefix}-hr`} className="my-6 border-neutral-30" />);
        continue;
      }

      // Code Blocks
      if (line.startsWith('```')) {
        closeList();
        const lang = line.substring(3).trim();
        let codeBlockContent = '';
        i++; 
        while (i < lines.length && !lines[i].startsWith('```')) {
          codeBlockContent += lines[i] + '\n';
          i++;
        }
        elements.push(
          <pre key={`pre-${lineKeyPrefix}`} className="code-block p-4 rounded-lg overflow-x-auto my-4 text-sm font-mono shadow-lg" style={{backgroundColor: 'var(--color-code-bg)', border: '1px solid var(--color-code-border)'}}>
            {lang && <div className="text-xs mb-2 capitalize font-semibold" style={{color: 'var(--color-code-lang)'}}>{lang}</div>}
            <code className="font-medium leading-relaxed block" style={{color: 'var(--color-code-text)'}}>
              {lang ? highlightCode(codeBlockContent.trimEnd(), lang) : codeBlockContent.trimEnd()}
            </code>
          </pre>
        );
        continue;
      }
      
      // Blockquotes
      if (line.startsWith('> ')) {
        closeList();
        let blockquoteContent = processInlineFormatting(line.substring(2), `${lineKeyPrefix}-bq-0`);
        const blockquoteLines: (string | JSX.Element)[][] = [blockquoteContent];
        let j = i + 1;
        while (j < lines.length && lines[j].startsWith('> ')) {
          blockquoteLines.push(processInlineFormatting(lines[j].substring(2), `${lineKeyPrefix}-bq-${j - i}`));
          j++;
        }
        elements.push(
          <blockquote key={`${lineKeyPrefix}-blockquote`} className="border-l-4 border-neutral-300 pl-4 italic my-4 text-neutral-600">
            {blockquoteLines.map((bqLine, bqIndex) => (
              <p key={bqIndex} className={bqIndex < blockquoteLines.length -1 ? 'mb-1' : ''}>{bqLine}</p>
            ))}
          </blockquote>
        );
        i = j - 1; // Move outer loop counter
        continue;
      }
      
      // Unordered List
      if (line.startsWith('- ') || line.startsWith('* ')) {
        if (currentListType !== 'ul') {
          closeList();
          currentListType = 'ul';
        }
        listItems.push(<li key={`${lineKeyPrefix}-li`}>{processInlineFormatting(line.substring(2), `${lineKeyPrefix}-li-content`)}</li>);
        continue;
      }

      // Ordered List
      if (line.match(/^\d+\.\s/)) {
        if (currentListType !== 'ol') {
          closeList();
          currentListType = 'ol';
        }
        listItems.push(<li key={`${lineKeyPrefix}-li`}>{processInlineFormatting(line.replace(/^\d+\.\s/, ''), `${lineKeyPrefix}-li-content`)}</li>);
        continue;
      }

      // Paragraphs (or other text)
      closeList(); 
      if (line.trim() === '') {
        // Render an empty paragraph for a double newline to create a paragraph break, or just skip
        // elements.push(<p key={`${lineKeyPrefix}-empty`} className="mb-4">&nbsp;</p>); // Example for explicit break
      } else {
        elements.push(<p key={`${lineKeyPrefix}-p`} className="mb-4 leading-relaxed">{processInlineFormatting(line, `${lineKeyPrefix}-p-content`)}</p>);
      }
    }
    closeList(); // Ensure any final list is closed
    return elements;
  };

  return <div className="markdown-content">{renderMarkdown(markdown)}</div>;
};

export default MarkdownRenderer;
