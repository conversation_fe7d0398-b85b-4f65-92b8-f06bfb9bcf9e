
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useProjects } from '../hooks/useProjects';
import { useNotifications } from '../contexts/NotificationContext'; // Added
import { geminiService } from '../services/geminiService';
import { META_PROMPT_FOR_DYNAMIC_APP_PROMPT_GENERATION, ICONS } from '../config'; // Updated import
import LoadingSpinner from './LoadingSpinner';
import { DYNAMIC_APP_FORM_SECTIONS_CONFIG, FormSectionConfig } from '../config/dynamicAppFormConfig';


export interface FormOption {
  value: string;
  labelKey: string; 
}

export interface FormFieldDefinition {
  name: string;
  labelKey: string;
  type: 'select' | 'text' | 'textarea';
  options?: FormOption[];
  defaultValue?: string;
  sectionKey: string;
  tooltipKey: string;
  placeholderKey?: string;
  required?: boolean;
}

interface FormSection { 
  key: string;
  title: string; 
  fields: FormFieldDefinition[];
}


const DynamicAppPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { showNotification } = useNotifications(); // Added
  const { addProject, isLoading: isProjectsLoadingGlobal } = useProjects();
  
  const formStructure: FormSection[] = useMemo(() => {
    return DYNAMIC_APP_FORM_SECTIONS_CONFIG.map(sectionConfig => ({
      key: sectionConfig.key,
      title: t(sectionConfig.key),
      fields: sectionConfig.fields.map(field => ({
        ...field,
      }))
    }));
  }, [t]);
  
  const initialFormData = useMemo(() => {
    const data: Record<string, string> = {};
    DYNAMIC_APP_FORM_SECTIONS_CONFIG.forEach(section => {
      section.fields.forEach(field => {
        data[field.name] = field.defaultValue || '';
      });
    });
    return data;
  }, []); 

  const [formData, setFormData] = useState<Record<string, string>>(initialFormData);
  const [generatedDeveloperPrompt, setGeneratedDeveloperPrompt] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [activeHelpTooltipKey, setActiveHelpTooltipKey] = useState<string | null>(null);
  const [activeHelpTooltipTitleKey, setActiveHelpTooltipTitleKey] = useState<string | null>(null);
  const [copySuccess, setCopySuccess] = useState<string>('');

  useEffect(() => {
  }, [i18n.language, initialFormData]);


  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleGeneratePrompt = useCallback(async () => {
    const primaryUseCaseField = DYNAMIC_APP_FORM_SECTIONS_CONFIG.flatMap(s => s.fields).find(f => f.name === 'PRIMARY_USE_CASE');
    if (primaryUseCaseField && (!formData[primaryUseCaseField.name] || !formData[primaryUseCaseField.name].trim())) {
      setError(t('dynamicApp.errorProblemDescriptionRequired'));
      showNotification({type: 'warning', title: t('notifications.inputRequiredTitle'), message: t('dynamicApp.errorProblemDescriptionRequired')});
      return;
    }

    setIsLoading(true);
    setError(null);
    setGeneratedDeveloperPrompt('');
    setCopySuccess('');
    try {
      const developerPrompt = await geminiService.generateDynamicAppDeveloperPrompt(
        formData,
        META_PROMPT_FOR_DYNAMIC_APP_PROMPT_GENERATION, 
        i18n.language
      );
      if (developerPrompt.startsWith(i18n.t('serviceMessages.apiKeyMissingError',{lng:i18n.language}).substring(0,10))) {
         const errorMessage = i18n.t('serviceMessages.apiKeyMissingErrorSummary',{lng:i18n.language});
         setError(errorMessage);
         setGeneratedDeveloperPrompt(errorMessage);
         showNotification({type: 'error', title: t('notifications.apiErrorTitle'), message: errorMessage });
      } else {
        setGeneratedDeveloperPrompt(developerPrompt);
      }
    } catch (err) {
      console.error("Failed to generate Dynamic App developer prompt:", err);
      const errorMessage = err instanceof Error ? err.message : t('dynamicApp.generationErrorGeneral');
      setError(errorMessage);
      setGeneratedDeveloperPrompt(t('dynamicApp.generationErrorInstruction', { error: errorMessage }));
      showNotification({type: 'error', title: t('dynamicApp.errorTitle'), message: errorMessage });
    } finally {
      setIsLoading(false);
    }
  }, [formData, i18n.language, t, showNotification]);

  const handleCreateProjectFromPrompt = async () => {
    if (!generatedDeveloperPrompt || generatedDeveloperPrompt.startsWith(t('serviceMessages.apiKeyMissingErrorSummary',{lng:i18n.language})) || error) {
      showNotification({type: 'error', title: t('notifications.creationFailedTitle'), message: t('dynamicApp.cannotCreateFromError')});
      return;
    }
    let projectName = t('dynamicApp.defaultProjectName');
    const namePatterns = [
        /^\*\*Application Name:\*\*\s*([^\n]+)/im, /^\*\*Názov aplikácie:\*\*\s*([^\n]+)/im,
        /^\*\*Project Title:\*\*\s*([^\n]+)/im, /^\*\*Názov Projektu:\*\*\s*([^\n]+)/im,
        /^Application Name:\s*([^\n]+)/im, /^Názov aplikácie:\s*([^\n]+)/im,
        /^Project Title:\s*([^\n]+)/im, /^Názov Projektu:\s*([^\n]+)/im,
    ];
    for (const pattern of namePatterns) {
        const match = generatedDeveloperPrompt.match(pattern);
        if (match && match[1] && match[1].trim()) {
            projectName = match[1].trim().split(/\s+-\s+/)[0].trim(); 
            break;
        }
    }
    
    const newProjectId = await addProject(projectName, generatedDeveloperPrompt, [], true);
    if (newProjectId) {
      showNotification({ type: 'success', title: t('notifications.projectCreatedSuccessTitle'), message: t('notifications.projectCreatedSuccessMessage', { projectName }) });
      navigate(`/project/${newProjectId}`);
    } else {
      showNotification({ type: 'error', title: t('notifications.creationFailedTitle'), message: t('dynamicApp.projectCreationError')});
    }
  };

  const showHelp = (field: FormFieldDefinition) => {
    setActiveHelpTooltipKey(field.tooltipKey);
    setActiveHelpTooltipTitleKey(field.labelKey);
  };

  const closeHelp = () => setActiveHelpTooltipKey(null);
  
  const handleCopyPrompt = () => {
    if (!generatedDeveloperPrompt) return;
    navigator.clipboard.writeText(generatedDeveloperPrompt)
      .then(() => setCopySuccess(t('dynamicApp.promptCopied')))
      .catch(() => {
        setCopySuccess(t('common.copyFailed', {defaultValue: 'Copy failed!'}))
        showNotification({type: 'error', title: t('notifications.copyErrorTitle'), message: t('common.copyFailed') });
      })
      .finally(() => setTimeout(() => setCopySuccess(''), 2000));
  };

  const renderFormField = (field: FormFieldDefinition) => {
    const fieldId = `dynamicApp_field_${field.name}`;
    const commonClasses = "mt-1 block w-full px-3 py-2 border border-neutral-30 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm bg-base-100";
    const placeholderText = t(field.placeholderKey || '', { defaultValue: '' });

    return (
      <div key={field.name} className="mb-4">
        <div className="flex items-center justify-between">
          <label htmlFor={fieldId} className="block text-sm font-medium text-neutral-70">
            {t(field.labelKey, { defaultValue: field.name.replace(/_/g, ' ') })}
            {field.required && <span className="text-error ml-1">*</span>}
          </label>
          <button type="button" onClick={() => showHelp(field)} className="p-1 text-primary hover:text-accent transition-colors"
            aria-label={t('common.showHelpFor', { fieldName: t(field.labelKey, { defaultValue: field.name.replace(/_/g, ' ') }) })}>
            {ICONS.INFO_CIRCLE_SOLID && React.cloneElement(ICONS.INFO_CIRCLE_SOLID, {className: "w-4 h-4"})}
          </button>
        </div>
        {field.type === 'select' && field.options && (
          <select id={fieldId} name={field.name} value={formData[field.name] || field.defaultValue} onChange={handleInputChange} className={commonClasses}>
            {field.options.map(opt => (
              <option key={opt.value} value={opt.value}>
                {t(opt.labelKey, { defaultValue: opt.labelKey.split('.').pop() })}
              </option>
            ))}
          </select>
        )}
        {field.type === 'textarea' && (
          <textarea id={fieldId} name={field.name} value={formData[field.name] || ''} onChange={handleInputChange} rows={3} className={commonClasses} placeholder={placeholderText} required={field.required} />
        )}
        {field.type === 'text' && (
           <input type="text" id={fieldId} name={field.name} value={formData[field.name] || ''} onChange={handleInputChange} className={commonClasses} placeholder={placeholderText} required={field.required} />
        )}
      </div>
    );
  };

  return (
    <div className="bg-base-100 p-6 sm:p-8 rounded-lg shadow-xl space-y-6 bg-secondary/70">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <h1 className="text-3xl font-bold text-primary mb-3 sm:mb-0 flex items-center">
          {ICONS.GRAPH && React.cloneElement(ICONS.GRAPH, { className: "w-8 h-8 mr-3 text-ai-technical"})} 
          {t('dynamicApp.pageTitle')}
        </h1>
      </div>
      <p className="text-neutral-70">{t('dynamicApp.pageDescription')}</p>
      
      {formStructure.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
            {formStructure.map(section => (
            <fieldset key={section.key} className="p-4 border border-neutral-30/50 rounded-md space-y-3 bg-base-100/50">
                <legend className="text-lg font-semibold text-primary px-2 -ml-2 mb-2">{section.title}</legend>
                {section.fields.map(renderFormField)}
            </fieldset>
            ))}
        </div>
      ) : <LoadingSpinner message={t('dynamicApp.loadingForm')} />}

      <button onClick={handleGeneratePrompt} disabled={isLoading || isProjectsLoadingGlobal || formStructure.length === 0}
        className="btn-primary inline-flex items-center px-6 py-3 text-base font-medium rounded-md shadow-lg hover:shadow-xl transition-shadow disabled:opacity-60">
        {isLoading ? <LoadingSpinner variant="dots" size="sm" color="text-base-100" icon={React.cloneElement(ICONS.AI_SPARKLE, { className: "w-4 h-4"})} iconClassName="w-4 h-4" containerClassName="flex flex-row items-center justify-center" messageClassName="ml-1.5"/> : ICONS.AI_SPARKLE && React.cloneElement(ICONS.AI_SPARKLE, {className: "w-5 h-5 mr-2 text-accent"})}
        <span className="ml-2">{t('dynamicApp.generatePromptButton')}</span>
      </button>
      
      {isLoading && <LoadingSpinner variant="dots" icon={React.cloneElement(ICONS.AI_SPARKLE, {className:"w-6 h-6 text-accent"})} message={t('dynamicApp.loadingMessage')} containerClassName="flex flex-row items-center justify-center space-x-3 my-8 p-4" messageClassName="text-lg text-neutral-700" />}
      {error && !isLoading && (
        <div className="p-4 mb-4 text-sm text-status-error bg-status-error/10 rounded-lg" role="alert">
          <span className="font-medium">{t('dynamicApp.errorTitle')}:</span> {error}
        </div>
      )}

      {!isLoading && generatedDeveloperPrompt && (
        <div className="space-y-6 pt-6 border-t border-neutral-30/50">
          <div className="p-4 border border-neutral-30/50 rounded-md bg-ai-developer-prompt/5 border-l-4 border-ai-developer-prompt shadow-sm">
            <div className="flex justify-between items-center mb-2">
                <h2 className="text-xl font-semibold text-neutral">{t('dynamicApp.generatedPromptTitle')}</h2>
                <button onClick={handleCopyPrompt} className="text-xs text-primary hover:underline flex items-center disabled:opacity-50"
                    disabled={!generatedDeveloperPrompt || !!copySuccess} title={t('dynamicApp.copyPrompt')}>
                    {ICONS.CLIPBOARD && React.cloneElement(ICONS.CLIPBOARD, {className: "w-4 h-4 mr-1"})}
                    {copySuccess || t('dynamicApp.copyPrompt')}
                </button>
            </div>
            <p className="text-xs text-neutral-70 mb-3">{t('dynamicApp.promptInstruction')}</p>
            <pre className="whitespace-pre-wrap break-words bg-base-100 p-3 rounded-md shadow-inner text-xs leading-relaxed text-neutral">
              {generatedDeveloperPrompt}
            </pre>
          </div>
          <div className="text-center">
            <button onClick={handleCreateProjectFromPrompt}
              disabled={isProjectsLoadingGlobal || isLoading || !!error || generatedDeveloperPrompt.startsWith(t('serviceMessages.apiKeyMissingErrorSummary',{lng:i18n.language}))}
              className="btn-accent inline-flex items-center px-6 py-3 text-base font-medium rounded-md shadow-lg hover:shadow-xl transition-shadow disabled:opacity-60">
              {isProjectsLoadingGlobal ? <LoadingSpinner size="xs" color="text-base-100" containerClassName='flex items-center' /> : ICONS.PROJECT && React.cloneElement(ICONS.PROJECT, {className: "w-5 h-5 mr-2"})}
              <span className="ml-2">{t('dynamicApp.createProjectButton')}</span>
            </button>
             {(generatedDeveloperPrompt.startsWith(t('serviceMessages.apiKeyMissingErrorSummary',{lng:i18n.language})) || (error && error.toLowerCase().includes(i18n.t('serviceMessages.apiKeyMissingError', {lng:i18n.language, defaultValue:"api key"}).toLowerCase().substring(0,10)))) && (
                <p className="text-status-error text-xs mt-2">{t('dynamicApp.apiKeyErrorNote')}</p>
            )}
          </div>
        </div>
      )}

      {activeHelpTooltipKey && activeHelpTooltipTitleKey && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[999] p-4" onClick={closeHelp} role="dialog" aria-modal="true" aria-labelledby="help-tooltip-title">
          <div className="bg-base-100 p-6 rounded-lg shadow-2xl w-full max-w-md max-h-[80vh] overflow-y-auto" onClick={(e) => e.stopPropagation()}>
            <h3 id="help-tooltip-title" className="text-lg font-semibold text-primary mb-3">
              {t(activeHelpTooltipTitleKey, {defaultValue: activeHelpTooltipTitleKey.split('.').pop()?.replace(/_/g, ' ')})}
            </h3>
            <p className="text-sm text-neutral-70 whitespace-pre-line">
              {t(activeHelpTooltipKey, {defaultValue: 'Help content not found.'})}
            </p>
            <div className="mt-6 text-right">
              <button onClick={closeHelp} className="px-4 py-2 text-sm font-medium text-neutral-70 bg-secondary hover:bg-neutral-30 rounded-md border border-neutral-30" aria-label={t('common.close')}>
                {t('common.close')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DynamicAppPage;
