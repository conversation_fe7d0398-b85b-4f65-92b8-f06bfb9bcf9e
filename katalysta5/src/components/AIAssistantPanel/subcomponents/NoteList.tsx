
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Project, AIAssistantNote } from '../../../types';
import { ICONS } from '../../../config';

interface NoteListProps {
  project: Project | null;
  onCreateMarkdownFileFromNote: (note: AIAssistantNote) => void;
}

const NoteList: React.FC<NoteListProps> = ({ project, onCreateMarkdownFileFromNote }) => {
  const { t, i18n } = useTranslation();

  if (!project || !project.dataCore.assistantNotes || project.dataCore.assistantNotes.length === 0) {
    return <p className="text-neutral-600 text-sm">{t('aiAssistant.notes.noNotes')}</p>;
  }

  return (
    <div className="space-y-3">
      {project.dataCore.assistantNotes.slice().reverse().map(note => (
        <details key={note.id} className="bg-secondary/50 p-3 rounded-md shadow-sm group">
          <summary className="text-sm font-semibold text-primary cursor-pointer group-open:mb-2 flex justify-between items-center">
            <div>
              {note.title}
              <span className="text-xs text-neutral-500 ml-2">({new Date(note.createdAt).toLocaleDateString(i18n.language)})</span>
            </div>
          </summary>
          <div className="text-xs space-y-1.5">
            {note.promptUsed && <p><strong>{t('aiAssistant.notes.promptUsedLabel')}</strong> {note.promptUsed}</p>}
            {note.contextProvided && <p><strong>{t('aiAssistant.notes.contextProvidedLabel')}</strong> <span className="line-clamp-2 italic">{note.contextProvided}</span></p>}
            <p><strong>{t('aiAssistant.notes.aiOutputLabel')}</strong></p>
            <pre className="whitespace-pre-wrap bg-base-100 p-2 rounded text-neutral-700 text-xs border border-neutral-200">{note.content}</pre>
            <button
              onClick={() => onCreateMarkdownFileFromNote(note)}
              className="mt-2 text-xs text-accent hover:text-accent/80 flex items-center px-2 py-1 bg-accent/10 rounded-md border border-accent/20 hover:border-accent/30"
              title={t('aiAssistant.notes.createMarkdownFileTooltip')}
            >
              {ICONS.DOCUMENT_TEXT && React.cloneElement(ICONS.DOCUMENT_TEXT, { className: "w-3.5 h-3.5 mr-1" })}
              {t('aiAssistant.notes.createMarkdownFile')}
            </button>
          </div>
        </details>
      ))}
    </div>
  );
};

export default NoteList;
