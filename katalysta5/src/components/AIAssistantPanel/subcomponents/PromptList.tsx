
import React from 'react';
import { AIAssistantPromptCategory, AIAssistantPrompt } from '../../../types';

interface PromptListProps {
  promptCategories: AIAssistantPromptCategory[];
  selectedPrompt: AIAssistantPrompt | null;
  onPromptSelect: (prompt: AIAssistantPrompt) => void;
}

const PromptList: React.FC<PromptListProps> = ({ promptCategories, selectedPrompt, onPromptSelect }) => {
  return (
    <>
      {promptCategories.map((category, catIndex) => (
        <div key={catIndex} className="mb-4">
          <h4 className="text-sm font-semibold text-neutral-600 mb-1.5">{category.categoryTitle}</h4>
          <ul className="space-y-1.5">
            {category.prompts.map(prompt => (
              <li key={prompt.id}>
                <button
                  onClick={() => onPromptSelect(prompt)}
                  className={`w-full text-left px-3 py-2 text-xs rounded-md transition-colors 
                              ${selectedPrompt?.id === prompt.id
                                  ? 'bg-primary text-base-100 shadow-sm'
                                  : 'bg-secondary hover:bg-primary/10 hover:text-primary'
                              }`}
                >
                  {prompt.title}
                  <p className={`text-xs mt-0.5 ${selectedPrompt?.id === prompt.id ? 'text-primary/70' : 'text-neutral-500'}`}>{prompt.description}</p>
                </button>
              </li>
            ))}
          </ul>
        </div>
      ))}
    </>
  );
};

export default PromptList;
