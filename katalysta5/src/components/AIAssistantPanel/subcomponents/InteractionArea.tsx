
import React from 'react';
import { useTranslation } from 'react-i18next';
import { AIAssistantPrompt } from '../../../types';
import LoadingSpinner from '../../LoadingSpinner';
import { ICONS } from '../../../config';

interface InteractionAreaProps {
  selectedPrompt: AIAssistantPrompt | null;
  userInput: string;
  onUserInputChanged: (value: string) => void;
  contextInput: string;
  onContextInputChanged: (value: string) => void;
  aiResponse: string | null;
  isLoadingResponse: boolean;
  isProjectsHookLoading: boolean;
  onExecutePrompt: () => void;
  responseContainerRef: React.RefObject<HTMLDivElement>;
  showSaveNoteFields: boolean;
  noteTitle: string;
  onNoteTitleChanged: (value: string) => void;
  onSaveNote: () => void;
  onOpenSaveNoteFields: () => void;
}

const InteractionArea: React.FC<InteractionAreaProps> = ({
  selectedPrompt,
  userInput,
  onUserInputChanged,
  contextInput,
  onContextInputChanged,
  aiResponse,
  isLoadingResponse,
  isProjectsHookLoading,
  onExecutePrompt,
  responseContainerRef,
  showSaveNoteFields,
  noteTitle,
  onNoteTitleChanged,
  onSaveNote,
  onOpenSaveNoteFields
}) => {
  const { t } = useTranslation();

  if (!selectedPrompt) {
    return (
      <div className="p-6 bg-secondary/30 rounded-md text-center text-neutral-600 flex flex-col items-center justify-center min-h-[200px]">
        {ICONS.LIGHTBULB && React.cloneElement(ICONS.LIGHTBULB, { className: "w-10 h-10 mb-3 text-neutral-400" })}
        <p>{t('aiAssistant.selectPromptToStart')}</p>
      </div>
    );
  }

  return (
    <>
      <div>
        <h4 className="text-md font-semibold text-primary mb-1">{selectedPrompt.title}</h4>
        <p className="text-xs text-neutral-500 mb-3">{selectedPrompt.description}</p>
      </div>

      {selectedPrompt.requiresUserInput && (
        <div>
          <label htmlFor="userInput" className="block text-sm font-medium text-neutral-700 mb-1">{t('aiAssistant.userInputLabel')}</label>
          <textarea
            id="userInput"
            value={userInput}
            onChange={(e) => onUserInputChanged(e.target.value)}
            rows={3}
            className="w-full p-2 border border-neutral-300 rounded-md shadow-sm text-sm focus:ring-primary focus:border-primary"
            placeholder={t('aiAssistant.userInputPlaceholder')}
          />
        </div>
      )}

      {selectedPrompt.requiresContextInput && (
        <div>
          <label htmlFor="contextInput" className="block text-sm font-medium text-neutral-700 mb-1">{t('aiAssistant.contextInputLabel')}</label>
          <textarea
            id="contextInput"
            value={contextInput}
            onChange={(e) => onContextInputChanged(e.target.value)}
            rows={5}
            className="w-full p-2 border border-neutral-300 rounded-md shadow-sm text-sm focus:ring-primary focus:border-primary"
            placeholder={t('aiAssistant.contextInputPlaceholder')}
          />
        </div>
      )}

      <button
        onClick={onExecutePrompt}
        disabled={isLoadingResponse || isProjectsHookLoading}
        className="btn-accent inline-flex items-center px-4 py-2 text-sm rounded-md shadow-sm disabled:opacity-60"
      >
        {isLoadingResponse || isProjectsHookLoading ? (
          <LoadingSpinner
            variant="dots"
            size="xs"
            color="text-base-100"
            icon={React.cloneElement(ICONS.AI_SPARKLE, { className: "w-3 h-3" })}
            iconClassName="w-3 h-3"
            containerClassName="flex flex-row items-center justify-center"
            messageClassName="ml-1.5"
          />
        ) : (
          <>
            {React.cloneElement(ICONS.AI_SPARKLE, { className: "w-4 h-4" })}
            <span className="ml-2">{t('aiAssistant.executePrompt')}</span>
          </>
        )}
      </button>

      <div ref={responseContainerRef}>
        {isLoadingResponse && !aiResponse && (
            <LoadingSpinner variant="dots" icon={React.cloneElement(ICONS.AI_SPARKLE, {className:"w-5 h-5 text-accent"})} message={t('aiAssistant.processing')} containerClassName="flex flex-row items-center justify-center space-x-2 p-4" />
        )}
        {aiResponse && (
          <div className="mt-4 p-3 border border-neutral-200 rounded-md bg-secondary/30">
            <h5 className="text-sm font-semibold text-neutral-800 mb-1.5">{t('aiAssistant.aiResponseTitle')}</h5>
            <pre className="whitespace-pre-wrap break-words text-sm text-neutral-700 leading-relaxed">{aiResponse}</pre>

            {!showSaveNoteFields && (
              <button
                onClick={onOpenSaveNoteFields}
                className="mt-3 text-xs text-primary hover:underline flex items-center"
              >
                {ICONS.SAVE && React.cloneElement(ICONS.SAVE, { className: "w-3.5 h-3.5 mr-1" })} {t('aiAssistant.saveThisResponse')}
              </button>
            )}

            {showSaveNoteFields && (
              <div className="mt-3 pt-3 border-t border-neutral-300 space-y-2">
                <div>
                  <label htmlFor="noteTitle" className="block text-xs font-medium text-neutral-600 mb-0.5">{t('aiAssistant.saveNoteTitleLabel')}</label>
                  <input
                    type="text"
                    id="noteTitle"
                    value={noteTitle}
                    onChange={(e) => onNoteTitleChanged(e.target.value)}
                    placeholder={t('aiAssistant.saveNoteTitlePlaceholder', { promptTitle: selectedPrompt.title })}
                    className="w-full p-1.5 border border-neutral-300 rounded-md text-xs focus:ring-primary focus:border-primary"
                  />
                </div>
                <button
                  onClick={onSaveNote}
                  className="btn-secondary px-3 py-1 text-xs rounded-md shadow-sm"
                >
                  {ICONS.SAVE && React.cloneElement(ICONS.SAVE, { className: "w-3.5 h-3.5 mr-1" })} {t('aiAssistant.saveResponseAsNote')}
                </button>
              </div>
            )}
          </div>
        )}
        {aiResponse && aiResponse.startsWith(t('serviceMessages.apiKeyMissingError')) && (
          <p className="text-status-error text-xs mt-1">{aiResponse}</p>
        )}
        {aiResponse && aiResponse.startsWith(t('serviceMessages.assistantExecutionError', { error: '' }).split(':')[0]) && (
          <p className="text-status-error text-xs mt-1">{aiResponse}</p>
        )}
      </div>
    </>
  );
};

export default InteractionArea;
