
import { useState, useCallback, useEffect, useRef }
from 'react';
import { useTranslation } from 'react-i18next';
import { Project, AIAssistantPrompt } from '../../../types';
import { useProjects } from '../../../hooks/useProjects';
import { useNotifications } from '../../../contexts/NotificationContext';

export const useAIAssistantPrompts = (
  project: Project | null,
  onResponseReceived?: (response: string) => void,
  getNoteTitlePlaceholder?: (promptTitle: string) => string
) => {
  const { t } = useTranslation();
  const { executeProjectAssistantPrompt, isLoading: isProjectsHookLoading } = useProjects();
  const { showNotification } = useNotifications();

  const [selectedPrompt, setSelectedPrompt] = useState<AIAssistantPrompt | null>(null);
  const [userInput, setUserInput] = useState<string>('');
  const [contextInput, setContextInput] = useState<string>('');
  const [aiResponse, setAiResponse] = useState<string | null>(null);
  const [isLoadingResponse, setIsLoadingResponse] = useState<boolean>(false);
  const responseContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (aiResponse && responseContainerRef.current) {
      responseContainerRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' });
    }
  }, [aiResponse]);

  const resetInteractionState = useCallback(() => {
    setAiResponse(null);
    setUserInput('');
    setContextInput('');
  }, []);

  const handlePromptSelect = useCallback((prompt: AIAssistantPrompt, onPromptSelected?: (prompt: AIAssistantPrompt) => void) => {
    setSelectedPrompt(prompt);
    resetInteractionState();
    if (onPromptSelected) {
        onPromptSelected(prompt);
    }
  }, [resetInteractionState]);

  const handleExecutePrompt = async () => {
    if (!project || !selectedPrompt) return;
    if (selectedPrompt.requiresUserInput && !userInput.trim()) {
      showNotification({ type: 'warning', title: t('notifications.inputRequiredTitle'), message: t('aiAssistant.errorUserInputRequired') });
      return;
    }

    setIsLoadingResponse(true);
    setAiResponse(null);
    // setShowSaveNoteFields(false); // This state is managed by useAIAssistantNotes

    const result = await executeProjectAssistantPrompt(project.id, selectedPrompt.promptTemplate, userInput, contextInput);
    setAiResponse(result);
    setIsLoadingResponse(false);
    if (onResponseReceived) {
      onResponseReceived(result);
    }
  };

  return {
    selectedPrompt,
    userInput,
    setUserInput,
    contextInput,
    setContextInput,
    aiResponse,
    setAiResponse,
    isLoadingResponse,
    isProjectsHookLoading,
    responseContainerRef,
    handlePromptSelect,
    handleExecutePrompt,
    resetInteractionState
  };
};
