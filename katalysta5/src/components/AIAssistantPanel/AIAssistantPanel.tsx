
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Project, AIAssistantPromptCategory, AIAssistantPrompt } from '../../types';
import LoadingSpinner from '../LoadingSpinner';
import { ICONS } from '../../config';

import { useAIAssistantTabs, AIAssistantTab } from './hooks/useAIAssistantTabs';
import { useAIAssistantPrompts } from './hooks/useAIAssistantPrompts';
import { useAIAssistantNotes } from './hooks/useAIAssistantNotes';
import PromptList from './subcomponents/PromptList';
import NoteList from './subcomponents/NoteList';
import InteractionArea from './subcomponents/InteractionArea';

interface AIAssistantPanelProps {
  project: Project | null;
}

const AIAssistantPanel: React.FC<AIAssistantPanelProps> = ({ project }) => {
  const { t } = useTranslation();

  const { noteTitle, setNoteTitle, showSaveNoteFields, handleSaveNote, handleCreateMarkdownFileFromNote, getNoteTitlePlaceholder, openSaveNoteFields, closeSaveNoteFields } = useAIAssistantNotes(project);
  
  const {
    selectedPrompt,
    userInput, setUserInput,
    contextInput, setContextInput,
    aiResponse, setAiResponse,
    isLoadingResponse,
    isProjectsHookLoading,
    responseContainerRef,
    handlePromptSelect, // Correctly use handlePromptSelect from the hook
    handleExecutePrompt,
    resetInteractionState
  } = useAIAssistantPrompts(project, () => openSaveNoteFields(selectedPrompt), getNoteTitlePlaceholder);
  
  const { activeTab, handleTabChange: changeTab } = useAIAssistantTabs('basic');

  const handleTabChangeInternal = (tab: AIAssistantTab) => {
    changeTab(tab, () => {
        handlePromptSelect(null); // Use handlePromptSelect from the hook
        resetInteractionState(); // Reset inputs and AI response
        closeSaveNoteFields(); // Close save note fields
    });
  };
  
  const handlePromptSelectInternal = (prompt: AIAssistantPromptCategory['prompts'][0]) => {
    handlePromptSelect(prompt, (selected) => {
        closeSaveNoteFields(); // Close save note fields when a new prompt is selected
        setNoteTitle(getNoteTitlePlaceholder(selected.title));
    });
  };

  const basicPrompts: AIAssistantPromptCategory[] = [
    {
      categoryTitle: t('aiAssistant.basicPrompts.titleSection'),
      prompts: [
        { id: 'basic_summarize', title: t('aiAssistant.basicPrompts.summarize.title'), description: t('aiAssistant.basicPrompts.summarize.description'), promptTemplate: "Summarize the following text concisely (1-2 paragraphs):\n\n{{userInput}}", category: 'Basic', requiresUserInput: true, requiresContextInput: false },
        { id: 'basic_keywords', title: t('aiAssistant.basicPrompts.extractKeywords.title'), description: t('aiAssistant.basicPrompts.extractKeywords.description'), promptTemplate: "Extract the 5-7 most important keywords from this text:\n\n{{userInput}}", category: 'Basic', requiresUserInput: true, requiresContextInput: false },
        { id: 'basic_explain', title: t('aiAssistant.basicPrompts.explainConcept.title'), description: t('aiAssistant.basicPrompts.explainConcept.description'), promptTemplate: "Explain the concept of '{{userInput}}' in simple terms, suitable for a beginner.", category: 'Basic', requiresUserInput: true, requiresContextInput: false },
      ]
    }
  ];

  if (!project) return <LoadingSpinner message={t('projectPage.loadingProject')} />;

  const isSuperAssistantReady = project.status === 'analyzed_deep' && project.strategicOutput?.suggestedAssistantPrompts && project.strategicOutput.suggestedAssistantPrompts.length > 0;

  return (
    <div className="bg-base-100 p-4 sm:p-6 rounded-lg shadow-xl mt-8">
      <h3 className="text-xl font-semibold text-neutral mb-1 flex items-center">
        {ICONS.WAND && React.cloneElement(ICONS.WAND, { className: "w-5 h-5 mr-2 text-accent" })}
        <span>{t('aiAssistant.title')}</span>
      </h3>
      <p className="text-xs text-neutral-500 mb-4">
        {activeTab === 'super' && !isSuperAssistantReady ? t('aiAssistant.superAssistantNotReadyDetailed') :
          activeTab === 'super' && isSuperAssistantReady ? t('aiAssistant.superAssistantDescription') :
            t('aiAssistant.basicAssistantDescription')
        }
      </p>

      <div className="flex border-b border-neutral-200 mb-4">
        {(['basic', 'super', 'notes'] as const).map(tab => (
          <button
            key={tab}
            onClick={() => handleTabChangeInternal(tab)}
            className={`px-3 py-2 text-sm font-medium -mb-px border-b-2 transition-colors
                        ${activeTab === tab
                ? 'border-primary text-primary'
                : 'border-transparent text-neutral-500 hover:text-primary hover:border-primary/30'
              }`}
            aria-current={activeTab === tab ? 'page' : undefined}
          >
            {t(`aiAssistant.tabs.${tab}`)}
          </button>
        ))}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1 space-y-4 max-h-[60vh] md:max-h-[calc(100vh-20rem)] overflow-y-auto pr-2 custom-scrollbar">
          {activeTab === 'basic' && (
            <PromptList
              promptCategories={basicPrompts}
              selectedPrompt={selectedPrompt}
              onPromptSelect={handlePromptSelectInternal}
            />
          )}
          {activeTab === 'super' && isSuperAssistantReady && project.strategicOutput?.suggestedAssistantPrompts && (
            <PromptList
              promptCategories={project.strategicOutput.suggestedAssistantPrompts}
              selectedPrompt={selectedPrompt}
              onPromptSelect={handlePromptSelectInternal}
            />
          )}
          {activeTab === 'super' && !isSuperAssistantReady && (
            <p className="text-neutral-600 text-sm p-4 bg-secondary/50 rounded-md">{t('aiAssistant.superAssistantNotReadyDetailed')}</p>
          )}
          {activeTab === 'notes' && (
            <NoteList
              project={project}
              onCreateMarkdownFileFromNote={handleCreateMarkdownFileFromNote}
            />
          )}
        </div>

        <div className="md:col-span-2 space-y-4">
          {activeTab !== 'notes' ? (
             <InteractionArea
                selectedPrompt={selectedPrompt}
                userInput={userInput}
                onUserInputChanged={setUserInput}
                contextInput={contextInput}
                onContextInputChanged={setContextInput}
                aiResponse={aiResponse}
                isLoadingResponse={isLoadingResponse}
                isProjectsHookLoading={isProjectsHookLoading}
                onExecutePrompt={handleExecutePrompt}
                responseContainerRef={responseContainerRef}
                showSaveNoteFields={showSaveNoteFields}
                noteTitle={noteTitle}
                onNoteTitleChanged={setNoteTitle}
                onSaveNote={() => handleSaveNote(aiResponse, selectedPrompt, contextInput)}
                onOpenSaveNoteFields={() => openSaveNoteFields(selectedPrompt)}
            />
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default AIAssistantPanel;
