
import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useProjects } from '../hooks/useProjects';
import { useNotifications } from '../contexts/NotificationContext';
import {
    Project,
    ProjectFile,
    ProjectTemplate,
    AISuggestion,
    GeneratedImageDetails,
    AIStrategicOutput,
    AIDevSpecFormData,
    BlogPostFormData,
    LandingPageFormData
} from '../types';
import LoadingSpinner from './LoadingSpinner';
import { ICONS } from '../config'; // Updated import
import KnowledgeGraphVisualizer from './KnowledgeGraphVisualizer';
import AISuggestionCard from './AISuggestionCard';
import AIAssistantPanel from './AIAssistantPanel';
import ProjectSideNav from './ProjectSideNav';
import MarkdownRenderer from './MarkdownRenderer';
import JS<PERSON><PERSON> from 'jszip';
import { geminiService } from '../services/geminiService';
import { LogoGenerationModal } from './LogoGenerationModal';
import { AIDeveloperSpecGeneratorModal } from './AIDeveloperSpecGeneratorModal';
import { BlogPostGeneratorModal } from './BlogPostGeneratorModal';
import { LandingPageGeneratorModal } from './LandingPageGeneratorModal';


interface PageSectionConfig {
  id: string;
  labelKey: string;
  icon: JSX.Element;
  aiBg?: string;
  insightType?: string;
}

const ProjectPage: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const { showNotification } = useNotifications();
  const {
    projects,
    getProjectById,
    runDeepAnalysis,
    isLoading: isProjectsHookLoadingGlobal,
    updateProjectDataCore,
    setSelectedTemplate: setProjectTemplateContext,
    addConceptualFileToProject,
    updateProjectGeneratedImage,
    generateAndSaveAIDevSpec,
    generateAndSaveBlogPost,
    generateAndSaveLandingPage,
  } = useProjects();

  const project = useMemo(() => projectId ? getProjectById(projectId) : null, [projectId, getProjectById, projects]);

  const [activeSection, setActiveSection] = useState<string | null>(null);
  const [newInsights, setNewInsights] = useState<Record<string, boolean>>({});

  const [isZipping, setIsZipping] = useState(false);

  const [isLogoModalOpen, setIsLogoModalOpen] = useState(false);
  const [isDevSpecModalOpen, setIsDevSpecModalOpen] = useState(false);
  const [isBlogPostModalOpen, setIsBlogPostModalOpen] = useState(false);
  const [isLandingPageModalOpen, setIsLandingPageModalOpen] = useState(false);

  const [isGeneratingAIDevSpecLocal, setIsGeneratingAIDevSpecLocal] = useState(false);
  const [isGeneratingBlogPostLocal, setIsGeneratingBlogPostLocal] = useState(false);
  const [isGeneratingLandingPageLocal, setIsGeneratingLandingPageLocal] = useState(false);
  const [isGeneratingDocumentationLocal, setIsGeneratingDocumentationLocal] = useState(false);


  const [showMainImagePrompt, setShowMainImagePrompt] = useState(false);
  const [isLoadingMainImage, setIsLoadingMainImage] = useState(false);
  const [isLoadingMainImageAlternatives, setIsLoadingMainImageAlternatives] = useState(false);

  const [isLoadingLogo, setIsLoadingLogo] = useState(false);
  const [isLoadingLogoAlternatives, setIsLoadingLogoAlternatives] = useState(false);

  const [isLoadingIcon, setIsLoadingIcon] = useState(false);
  const [isLoadingBanner, setIsLoadingBanner] = useState(false);

  const sectionRefs = useRef<Record<string, HTMLElement | null>>({});

  const isAnalyzing = useMemo(() =>
    project?.status === 'analyzing_deep' ||
    project?.status === 'analyzing_initial' ||
    project?.status?.startsWith('analyzing_deep_'),
    [project?.status]
  );

  const markdownBlueprintFile = useMemo(() => {
    if (!project) return null;
    return project.files.find(f => f.type === 'text/markdown' && f.content && (f.name.includes('Blueprint') || f.name.includes('Developer_Prompt') || f.name.includes('Developer_Blueprint')));
  }, [project]);

  const documentationOutlineFile = useMemo(() => {
    if (!project) return null;
    const outlineFileName = t('projectPage.documentationOutlineFileName', { projectName: project.name, defaultValue: `${project.name}_Documentation_Outline.md` });
    return project.files.find(f => f.name.includes(outlineFileName));
  }, [project, t]);

  const blogPostFile = useMemo(() => {
    if (!project) return null;
    const blogPostFileName = t('projectPage.blogPostFileName', { projectName: project.name.replace(/[^a-z0-9]/gi, '_'), defaultValue: `${project.name}_Blog_Post.md` });
    return project.files.find(f => f.name.includes(blogPostFileName));
  }, [project, t]);

  const landingPageDetails = useMemo(() => {
    if (!project) return { content: null, file: null, specFile: null };
    const landingPageFileName = t('projectPage.landingPage.fileNameDefaultHTML', {projectName: project.name.replace(/[^a-z0-9]/gi, '_')});
    const landingPageSpecFileName = t('projectPage.landingPage.specFileNameDefaultMD', {projectName: project.name.replace(/[^a-z0-9]/gi, '_')});
    const file = project.files.find(f => f.name === landingPageFileName);
    const specFile = project.files.find(f => f.name === landingPageSpecFileName);
    return { content: file?.content || null, file: file || null, specFile: specFile || null };
  }, [project, t]);

  const aiDeveloperSpecFile = useMemo(() => {
    if (!project) return null;
    const specFileName = t('projectPage.aiDeveloperSpecFileName', { projectName: project.name.replace(/[^a-z0-9]/gi, '_'), defaultValue: `${project.name}_AI_Developer_Spec.md` });
    return project.files.find(f => f.name.includes(specFileName));
  }, [project, t]);


  const shouldShowSection = useCallback((sectionId: string): boolean => {
    if (!project) return false;

    if (sectionId === 'project-blueprint') return !!markdownBlueprintFile?.content;
    if (sectionId === 'ai-developer-specification') return !!aiDeveloperSpecFile?.content || isGeneratingAIDevSpecLocal;
    if (sectionId === 'blog-post') return !!blogPostFile?.content || isGeneratingBlogPostLocal;
    if (sectionId === 'landing-page') return !!landingPageDetails.file || !!landingPageDetails.specFile || isGeneratingLandingPageLocal;

    if (['project-header', 'ai-strategic-center', 'ai-assistant-panel', 'project-files', 'generated-image-section', 'ai-asset-generator'].includes(sectionId)) return true;

    if (project.status === 'analyzed_deep') return true;
    if (isAnalyzing && ['project-narrative', 'knowledge-graph', 'strategic-suggestions', 'display-template'].includes(sectionId)) return true;

    return false;
  }, [project, markdownBlueprintFile, aiDeveloperSpecFile, blogPostFile, landingPageDetails, isAnalyzing, isGeneratingAIDevSpecLocal, isGeneratingBlogPostLocal, isGeneratingLandingPageLocal]);


  const pageSections = useMemo((): PageSectionConfig[] => {
    const baseSectionsList: PageSectionConfig[] = [
      { id: 'project-header', labelKey: 'projectPage.nav.header', icon: ICONS.PROJECT },
    ];

    if (shouldShowSection('project-blueprint')) {
        baseSectionsList.push({ id: 'project-blueprint', labelKey: 'projectPage.nav.blueprint', icon: ICONS.DOCUMENT_TEXT, aiBg: 'bg-ai-developer-prompt/5' });
    }
    baseSectionsList.push({ id: 'ai-asset-generator', labelKey: 'projectPage.nav.aiAssetGenerator', icon: ICONS.AI_SPARKLE, aiBg: 'bg-accent/5' });

    if (shouldShowSection('ai-developer-specification')) {
      baseSectionsList.push({ id: 'ai-developer-specification', labelKey: 'projectPage.nav.aiDeveloperSpecification', icon: ICONS.CODE_BLOCK, aiBg: 'bg-ai-technical/5' });
    }
    if (shouldShowSection('blog-post')) {
        baseSectionsList.push({ id: 'blog-post', labelKey: 'projectPage.nav.blogPost', icon: ICONS.PENCIL_SQUARE, aiBg: 'bg-ai-creative/5' });
    }
    if (shouldShowSection('landing-page')) {
        baseSectionsList.push({ id: 'landing-page', labelKey: 'projectPage.nav.landingPage', icon: ICONS.LINK, aiBg: 'bg-ai-synergy/5' });
    }

    baseSectionsList.push(
        { id: 'ai-strategic-center', labelKey: 'projectPage.nav.strategicCenter', icon: ICONS.AI_SPARKLE },
        { id: 'ai-assistant-panel', labelKey: 'projectPage.nav.assistantPanel', icon: ICONS.WAND },
        { id: 'project-narrative', labelKey: 'projectPage.nav.narrative', icon: ICONS.BOOK_OPEN, insightType: 'narrative', aiBg: 'bg-ai-creative/5' },
        { id: 'generated-image-section', labelKey: 'projectPage.nav.imageSection', icon: ICONS.IMAGE, insightType: 'image', aiBg: 'bg-ai-creative/5' },
        { id: 'knowledge-graph', labelKey: 'projectPage.nav.knowledgeGraph', icon: ICONS.GRAPH, insightType: 'knowledgeGraph', aiBg: 'bg-ai-technical/5' },
        { id: 'strategic-suggestions', labelKey: 'projectPage.nav.suggestions', icon: ICONS.LIGHTBULB, insightType: 'suggestions', aiBg: 'bg-ai-next-step/5' },
        { id: 'display-template', labelKey: 'projectPage.nav.template', icon: ICONS.PROJECT, insightType: 'template', aiBg: 'bg-ai-next-step/5' },
        { id: 'project-files', labelKey: 'projectPage.nav.files', icon: ICONS.FOLDER }
    );
    return baseSectionsList;
  }, [t, shouldShowSection]);

  const availableSectionsForNav = useMemo(() => {
    return pageSections.map(s => ({
      ...s,
      hasNewInsight: !!(s.insightType && newInsights[s.insightType]) || (s.id === 'ai-strategic-center' && Object.values(newInsights).some(v=>v))
    })).filter(s => shouldShowSection(s.id));
  }, [pageSections, newInsights, shouldShowSection]);


  useEffect(() => {
    if (!project && projectId) {
      navigate('/');
    }
  }, [project, projectId, navigate]);


  const prevStrategicOutputRef = useRef<AIStrategicOutput | undefined>(undefined);
  useEffect(() => {
    if (project?.strategicOutput && project.strategicOutput !== prevStrategicOutputRef.current) {
        const newAvailableInsights: Record<string, boolean> = {};
        if (project.strategicOutput.projectPageNarrative) newAvailableInsights['narrative'] = true;
        if (project.strategicOutput.generatedImage?.url) newAvailableInsights['image'] = true;

        if (project.strategicOutput.knowledgeGraph?.summary) newAvailableInsights['knowledgeGraph'] = true;
        if (project.strategicOutput.strategicSuggestions?.length > 0) newAvailableInsights['suggestions'] = true;
        if (project.strategicOutput.suggestedTemplate) newAvailableInsights['template'] = true;

        setNewInsights(prev => ({ ...prev, ...newAvailableInsights }));
        prevStrategicOutputRef.current = project.strategicOutput;
    }
  }, [project?.strategicOutput]);


 useEffect(() => {
    if (project && availableSectionsForNav.length > 0 && !activeSection) {
        setActiveSection(availableSectionsForNav[0].id);
    }
 }, [project, availableSectionsForNav, activeSection]);

  const handleNavClick = (sectionId: string) => {
    setActiveSection(sectionId);
    const element = sectionRefs.current[sectionId];
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
    if (newInsights[sectionId]) {
        setNewInsights(prev => ({...prev, [sectionId]: false}));
    }
    if (sectionId === 'ai-strategic-center' && Object.values(newInsights).some(v=>v)) {
      setNewInsights({}); // Clear all if main AI center is clicked
    }
  };

  const handleRunDeepAnalysis = async () => {
    if (project) {
      await runDeepAnalysis(project.id);
    }
  };

  const handleSaveChanges = () => {
    if (project) {
      // Persist changes in dataCore or template if needed.
      // For demo purposes, updateProjectDataCore is used within specific actions.
      showNotification({type: 'success', title: t('notifications.changesSavedTitle'), message: t('projectPage.saveChangesAlert') });
      console.log("Updated project data core:", project.dataCore);
    }
  };

  const handleTemplateChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (project) {
      const newTemplate = e.target.value as ProjectTemplate;
      setProjectTemplateContext(project.id, newTemplate);
    }
  };

  const handleDownloadAllFiles = async () => {
    if (!project || !project.files || project.files.length === 0) {
      showNotification({ type: 'info', message: t('projectPage.noFilesToZip') });
      return;
    }

    const filesWithContent = project.files.filter(file => file.content || (file.type.startsWith('image/') && project.strategicOutput?.generatedImage?.url === file.name) ); // Simplified check

    if (filesWithContent.length === 0) {
      showNotification({ type: 'info', message: t('projectPage.noContentToZip') });
      return;
    }
    
    setIsZipping(true);
    showNotification({ type: 'info', title: t('projectPage.generatingZip'), message: t('projectPage.generatingZipMessage')});

    try {
      const zip = new JSZip();
      for (const file of project.files) {
        if (file.content) {
          zip.file(file.name, file.content);
        } else if (file.type.startsWith('image/')) {
            let imageUrlToFetch: string | undefined = undefined;
            if (project.strategicOutput?.generatedImage?.url && file.name === "project_main_image.jpg") imageUrlToFetch = project.strategicOutput.generatedImage.url;
            else if (project.strategicOutput?.generatedLogo?.url && file.name === "project_logo.jpg") imageUrlToFetch = project.strategicOutput.generatedLogo.url;
            else if (project.strategicOutput?.generatedIcon?.url && file.name === "project_icon.jpg") imageUrlToFetch = project.strategicOutput.generatedIcon.url;
            else if (project.strategicOutput?.generatedBanner?.url && file.name === "project_banner.jpg") imageUrlToFetch = project.strategicOutput.generatedBanner.url;

            if (imageUrlToFetch && imageUrlToFetch.startsWith('data:image')) {
                const base64Data = imageUrlToFetch.split(',')[1];
                zip.file(file.name, base64Data, {base64: true});
            }
        }
      }
      const zipBlob = await zip.generateAsync({ type: 'blob' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(zipBlob);
      const zipFileName = `${project.name.replace(/[^a-z0-9]/gi, '_')}_AI_Catalyst_Files.zip`;
      link.download = zipFileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);
      showNotification({ type: 'success', title: t('notifications.downloadSuccessTitle'), message: t('notifications.zipGeneratedMessage', {fileName: zipFileName})});
    } catch (error) {
      console.error("Error zipping files:", error);
      showNotification({ type: 'error', title: t('notifications.zipErrorTitle'), message: t('projectPage.zipError') });
    } finally {
      setIsZipping(false);
    }
  };

  const handleGenerateDocumentation = async () => {
    if (!project || !markdownBlueprintFile?.content) {
      showNotification({ type: 'warning', title: t('notifications.inputRequiredTitle'), message: t('projectPage.deepAnalysisRequiredForFeature')});
      return;
    }
    showNotification({ type: 'info', title: t('projectPage.generatingDocumentation'), message: t('projectPage.generatingDocumentation') });
    setIsGeneratingDocumentationLocal(true); 
    try {
      const outline = await geminiService.generateDocumentationOutline(markdownBlueprintFile.content, i18n.language);
      const outlineFileName = t('projectPage.documentationOutlineFileName', {projectName: project.name, defaultValue: `${project.name}_Documentation_Outline.md`});
      addConceptualFileToProject(project.id, outlineFileName, 'text/markdown', outline.length, outline);
      showNotification({ type: 'success', title: t('notifications.generationSuccessTitle'), message: t('projectPage.documentationOutlineGenerated') });
    } catch(error: any) {
      showNotification({ type: 'error', title: t('notifications.generationFailedTitle'), message: error.message || t('projectPage.generateDocumentationError') });
    } finally {
      setIsGeneratingDocumentationLocal(false);
    }
  };
  
  const handleGenerateImage = async (imageType: 'main' | 'logo' | 'icon' | 'banner', existingPrompt?: string) => {
    if (!project) return;
    const currentLang = i18n.language;

    const setLoadingState = (loading: boolean) => {
        switch(imageType) {
            case 'main': setIsLoadingMainImage(loading); break;
            case 'logo': setIsLoadingLogo(loading); break;
            case 'icon': setIsLoadingIcon(loading); break;
            case 'banner': setIsLoadingBanner(loading); break;
        }
    };
    setLoadingState(true);
    showNotification({ type: 'info', title: t('projectPage.imageGeneration.generating', { asset: t(`projectPage.nav.${imageType === 'main' ? 'imageSection' : imageType === 'logo' ? 'projectLogo' : imageType === 'icon' ? 'projectIcon' : 'projectBanner'}`) }), message: t('projectPage.imageGeneration.generating', { asset: t(`projectPage.nav.${imageType === 'main' ? 'imageSection' : imageType === 'logo' ? 'projectLogo' : imageType === 'icon' ? 'projectIcon' : 'projectBanner'}`) }) });

    try {
      let imageDetails: GeneratedImageDetails | undefined;
      if (imageType === 'main') {
        imageDetails = await geminiService.generateProjectImage(project, currentLang);
      } else if (imageType === 'logo') {
        // If a logo brief exists, use it. Otherwise, use generic logo generation.
        if (project.dataCore.logoBrief) {
          imageDetails = await geminiService.generateLogoFromBrief(project.dataCore.logoBrief, project, currentLang);
        } else {
          imageDetails = await geminiService.generateLogo(project, currentLang); // Generic logo
        }
      } // Add icon and banner later

      if (imageDetails?.url) {
        updateProjectGeneratedImage(project.id, imageType, imageDetails);
        showNotification({ type: 'success', title: t('notifications.generationSuccessTitle'), message: t('notifications.imageGeneratedSuccess', {assetName: t(`projectPage.nav.${imageType === 'main' ? 'imageSection' : imageType === 'logo' ? 'projectLogo' : imageType === 'icon' ? 'projectIcon' : 'projectBanner'}` )}) });
      } else {
        throw new Error(t('projectPage.imageGeneration.generationFailed', {asset: imageType}));
      }
    } catch (error: any) {
      console.error(`Error generating ${imageType} image:`, error);
      showNotification({ type: 'error', title: t('notifications.generationFailedTitle'), message: t('projectPage.imageGeneration.generationFailedError', {asset: imageType, error: error.message}) });
    } finally {
      setLoadingState(false);
    }
  };

  const handleGenerateImageAlternatives = async (imageType: 'main' | 'logo' | 'icon' | 'banner') => {
    if (!project) return;
    const currentLang = i18n.language;
    const existingImageDetails = imageType === 'main' ? project.strategicOutput?.generatedImage :
                               imageType === 'logo' ? project.strategicOutput?.generatedLogo :
                               imageType === 'icon' ? project.strategicOutput?.generatedIcon :
                               project.strategicOutput?.generatedBanner;

    if (!existingImageDetails?.prompt) {
        showNotification({ type: 'warning', title: t('notifications.noBasePromptTitle'), message: t('projectPage.imageGeneration.noBasePrompt', {asset: imageType}) });
        return;
    }
    const setLoadingState = (loading: boolean) => {
        switch(imageType) {
            case 'main': setIsLoadingMainImageAlternatives(loading); break;
            case 'logo': setIsLoadingLogoAlternatives(loading); break;
            // Add icon and banner later
        }
    };
    setLoadingState(true);
    showNotification({ type: 'info', title: t('projectPage.imageGeneration.generatingAlternatives', {asset: imageType}), message: t('projectPage.imageGeneration.generatingAlternatives', {asset: imageType}) });

    try {
        const alternatives = await geminiService.generateImageAlternatives(existingImageDetails.prompt, currentLang, 3);
        if (alternatives.length > 0) {
            updateProjectGeneratedImage(project.id, imageType, { ...existingImageDetails, alternatives });
            showNotification({ type: 'success', title: t('notifications.generationSuccessTitle'), message: t('notifications.alternativesGeneratedSuccess', {assetName: imageType})});
        } else {
             showNotification({ type: 'info', title: t('notifications.noAlternativesFoundTitle'), message: t('projectPage.imageGeneration.noAlternatives', {asset: imageType})});
        }
    } catch (error: any) {
        console.error(`Error generating ${imageType} alternatives:`, error);
        showNotification({ type: 'error', title: t('notifications.generationFailedTitle'), message: error.message });
    } finally {
        setLoadingState(false);
    }
  };

  const handleSelectAlternative = (imageType: 'main' | 'logo' | 'icon' | 'banner', alternativeUrl: string) => {
    if (!project) return;
    const existingImageDetails = imageType === 'main' ? project.strategicOutput?.generatedImage :
                               imageType === 'logo' ? project.strategicOutput?.generatedLogo :
                               imageType === 'icon' ? project.strategicOutput?.generatedIcon :
                               project.strategicOutput?.generatedBanner;
    if (existingImageDetails) {
        updateProjectGeneratedImage(project.id, imageType, { ...existingImageDetails, url: alternativeUrl, alternatives: [] /* Clear alternatives after selection or keep them? */ });
        showNotification({ type: 'success', title: t('notifications.updateSuccessTitle'), message: t('notifications.imageAlternativeSelected')});
    }
  };

  const renderImageSection = (
        imageType: 'main' | 'logo' | 'icon' | 'banner',
        imageDetails: GeneratedImageDetails | undefined,
        titleKey: string,
        altKey: string,
        isLoadingThisImage: boolean,
        isLoadingAlternativesThisImage: boolean
    ) => {
    if (!project) return null;

    return (
      <section id={`generated-${imageType}-image`} ref={el => { sectionRefs.current[`generated-${imageType}-image`] = el; }} className="pt-20 -mt-20">
        <h3 className="text-xl font-semibold text-neutral mb-3 flex items-center">
          {ICONS.IMAGE && React.cloneElement(ICONS.IMAGE, {className: "w-5 h-5 mr-2"})}
          {t(titleKey)}
        </h3>
        {isLoadingThisImage ? (
          <LoadingSpinner message={t('projectPage.imageGeneration.generating', { asset: t(titleKey) })} />
        ) : imageDetails?.url ? (
          <div className="p-4 bg-base-100 border border-neutral-200 rounded-md shadow">
            <img src={imageDetails.url} alt={t(altKey, { projectName: project.name })} className="w-full h-auto max-h-96 object-contain rounded-md mb-3" />
            <div className="text-xs text-neutral-600 mb-3">
              <button onClick={() => imageType === 'main' ? setShowMainImagePrompt(!showMainImagePrompt) : null /* Similar state for other types if needed */ } className="hover:underline text-primary">
                {showMainImagePrompt ? t('projectPage.imageGeneration.hidePrompt') : t('projectPage.imageGeneration.showPrompt')}
              </button>
              {showMainImagePrompt && <pre className="mt-1 p-2 bg-secondary rounded-md whitespace-pre-wrap">{imageDetails.prompt}</pre>}
            </div>
            <div className="flex flex-wrap gap-2">
              <button onClick={() => handleGenerateImage(imageType, imageDetails.prompt)} disabled={isLoadingThisImage || isLoadingAlternativesThisImage} className="btn-secondary text-xs px-3 py-1.5">
                {isLoadingThisImage ? <LoadingSpinner size="xs"/> : ICONS.REFRESH && React.cloneElement(ICONS.REFRESH, {className:"w-3.5 h-3.5"})}
                <span className="ml-1.5">{t('projectPage.imageGeneration.regenerateButton')}</span>
              </button>
              <button onClick={() => handleGenerateImageAlternatives(imageType)} disabled={isLoadingThisImage || isLoadingAlternativesThisImage} className="btn-secondary text-xs px-3 py-1.5">
                {isLoadingAlternativesThisImage ? <LoadingSpinner size="xs"/> : ICONS.PALETTE && React.cloneElement(ICONS.PALETTE, {className:"w-3.5 h-3.5"})}
                <span className="ml-1.5">{t('projectPage.imageGeneration.alternativesButton')}</span>
              </button>
            </div>
            {imageDetails.alternatives && imageDetails.alternatives.length > 0 && (
              <div className="mt-4">
                <h4 className="text-sm font-semibold text-neutral-700 mb-2">{t('projectPage.imageGeneration.alternativesTitle')}</h4>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                  {imageDetails.alternatives.map((altUrl, index) => (
                    <button key={index} onClick={() => handleSelectAlternative(imageType, altUrl)} className="border-2 border-transparent hover:border-primary rounded-md overflow-hidden focus:outline-none focus:ring-2 focus:ring-primary">
                      <img src={altUrl} alt={`${t('projectPage.imageGeneration.alternativeText')} ${index + 1}`} className="w-full h-auto object-cover" />
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="p-4 bg-base-100 border-2 border-dashed border-neutral-200 rounded-md text-center">
            <p className="text-sm text-neutral-500 mb-3">{t('projectPage.imageGeneration.noImageYet', {asset: t(titleKey)})}</p>
            <button onClick={() => imageType === 'logo' ? setIsLogoModalOpen(true) : handleGenerateImage(imageType)} disabled={isLoadingThisImage} className="btn-primary text-xs px-3 py-1.5">
              {ICONS.WAND && React.cloneElement(ICONS.WAND, {className:"w-3.5 h-3.5"})}
              <span className="ml-1.5">{t('projectPage.imageGeneration.generateButton')}</span>
            </button>
          </div>
        )}
      </section>
    );
  };


  if (!project && isProjectsHookLoadingGlobal) {
    return <LoadingSpinner message={t('projectPage.loadingProject')} />;
  }
  if (!project) {
     return <div className="text-center p-8">{t('projectPage.projectNotFound')} <Link to="/" className="text-primary hover:underline">{t('projectPage.goToDashboard')}</Link></div>;
  }


  const isAnyAssetGeneratorLoading = isProjectsHookLoadingGlobal || isGeneratingAIDevSpecLocal || isGeneratingBlogPostLocal || isGeneratingLandingPageLocal;

  return (
    <div className="flex flex-col md:flex-row gap-6">
      <ProjectSideNav
        sections={availableSectionsForNav}
        onNavClick={handleNavClick}
        activeSectionId={activeSection}
      />
      <div className="flex-1 space-y-8 min-w-0"> {/* Added min-w-0 for flex child to prevent overflow */}
        <section id="project-header" ref={el => { sectionRefs.current['project-header'] = el; }} className="bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl">
          <div className="flex flex-col md:flex-row justify-between md:items-start mb-4 pb-4 border-b border-neutral-200/70">
            <div className="flex-1 min-w-0"> {/* Added min-w-0 */}
              <h1 className="text-2xl sm:text-3xl font-bold text-primary mb-1 truncate">{project.name}</h1>
              <p className="text-sm text-neutral-600 line-clamp-3">{project.description}</p>
              <p className="text-xs text-neutral-500 mt-1.5">
                {t('projectPage.created')}: {new Date(project.createdAt).toLocaleDateString(i18n.language)} | {t('projectPage.lastUpdated')}: {new Date(project.lastInteractionTimestamp || project.updatedAt).toLocaleString(i18n.language)}
              </p>
            </div>
            <div className="mt-4 md:mt-0 md:ml-4 flex-shrink-0">
              <button
                onClick={handleSaveChanges}
                className="btn-primary inline-flex items-center px-3 py-1.5 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium rounded-md shadow-sm"
              >
                {ICONS.SAVE} <span className="ml-2">{t('projectPage.saveChanges')}</span>
              </button>
            </div>
          </div>
          {/* Status display */}
          <div className="text-xs font-medium">
            Status: <span className={`font-semibold px-2 py-1 rounded-full ${project.status === 'error' ? 'bg-status-error/20 text-status-error' : project.status.includes('analyzing') ? 'bg-status-pending/20 text-status-pending animate-pulse' : 'bg-status-success/20 text-status-success'}`}>
                {project.detailedStatusMessageKey ? t(project.detailedStatusMessageKey) : t(`projectCard.status${project.status.charAt(0).toUpperCase() + project.status.slice(1).replace(/_([a-z])/g, (g) => g[1].toUpperCase())}` as any, project.status)}
            </span>
            {project.status === 'error' && project.errorDetails && <p className="text-error/80 mt-1">{project.errorDetails}</p>}
          </div>
        </section>
        
        {shouldShowSection('project-blueprint') && markdownBlueprintFile?.content && (
          <section id="project-blueprint" ref={el => { sectionRefs.current['project-blueprint'] = el; }} className="pt-20 -mt-20 bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl">
            <h3 className="text-xl font-semibold text-neutral mb-3 flex items-center">
              {ICONS.DOCUMENT_TEXT && React.cloneElement(ICONS.DOCUMENT_TEXT, {className: "w-5 h-5 mr-2 text-ai-developer-prompt"})}
              {t('projectPage.nav.blueprint')}
            </h3>
            <div className="prose prose-sm max-w-none p-3 bg-ai-developer-prompt/5 border border-ai-developer-prompt/20 rounded-md shadow-inner">
              <MarkdownRenderer markdown={markdownBlueprintFile.content} />
            </div>
          </section>
        )}

        {shouldShowSection('ai-asset-generator') && (
          <section id="ai-asset-generator" ref={el => { sectionRefs.current['ai-asset-generator'] = el; }} className="pt-20 -mt-20 bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl">
            <h3 className="text-xl font-semibold text-neutral mb-3 flex items-center">
                {React.cloneElement(ICONS.AI_SPARKLE, { className: "w-6 h-6 mr-2 text-accent" })}
                {t('projectPage.assetGenerator.title')}
            </h3>
            <p className="text-sm text-neutral-600 mb-4">{t('projectPage.assetGenerator.description')}</p>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-3 sm:gap-4">
                <button onClick={() => setIsLogoModalOpen(true)} className="btn-secondary w-full flex items-center justify-center p-2.5 text-sm" disabled={isAnyAssetGeneratorLoading}>
                    {React.cloneElement(ICONS.PAINT_BRUSH, { className: "w-4 h-4 mr-2" })} {t('projectPage.assetGenerator.generateLogoButton')}
                </button>
                <button onClick={() => setIsDevSpecModalOpen(true)} className="btn-secondary w-full flex items-center justify-center p-2.5 text-sm" disabled={isAnyAssetGeneratorLoading}>
                    {React.cloneElement(ICONS.CPU_CHIP, { className: "w-4 h-4 mr-2" })} {t('projectPage.assetGenerator.generateAIDevSpecButton')}
                </button>
                <button onClick={() => setIsBlogPostModalOpen(true)} className="btn-secondary w-full flex items-center justify-center p-2.5 text-sm" disabled={isAnyAssetGeneratorLoading}>
                    {React.cloneElement(ICONS.PENCIL_SQUARE, { className: "w-4 h-4 mr-2" })} {t('projectPage.assetGenerator.generateBlogPostButton')}
                </button>
                <button onClick={() => setIsLandingPageModalOpen(true)} className="btn-secondary w-full flex items-center justify-center p-2.5 text-sm" disabled={isAnyAssetGeneratorLoading}>
                    {React.cloneElement(ICONS.LINK, { className: "w-4 h-4 mr-2" })} {t('projectPage.assetGenerator.generateLandingPageButton')}
                </button>
            </div>
          </section>
        )}
        
        {shouldShowSection('ai-developer-specification') && (
           <section id="ai-developer-specification" ref={el => { sectionRefs.current['ai-developer-specification'] = el; }} className="pt-20 -mt-20 bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl">
                <h3 className="text-xl font-semibold text-neutral mb-3 flex items-center">
                    {React.cloneElement(ICONS.CODE_BLOCK, { className: "w-5 h-5 mr-2 text-ai-technical" })}
                    {t('projectPage.aiDeveloperSpecificationTitle')}
                </h3>
                {isGeneratingAIDevSpecLocal ? (
                    <LoadingSpinner message={t('projectPage.generatingAIDevSpec')} />
                ) : aiDeveloperSpecFile?.content ? (
                    <div className="prose prose-sm max-w-none p-3 bg-ai-technical/5 border border-ai-technical/20 rounded-md shadow-inner">
                        <MarkdownRenderer markdown={aiDeveloperSpecFile.content} />
                    </div>
                ) : (
                    <p className="text-sm text-neutral-500">{t('projectPage.assetGenerator.contentNotYetGenerated', { assetName: t('projectPage.aiDeveloperSpecificationTitle') })}</p>
                )}
            </section>
        )}

        {shouldShowSection('blog-post') && (
             <section id="blog-post" ref={el => { sectionRefs.current['blog-post'] = el; }} className="pt-20 -mt-20 bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl">
                <h3 className="text-xl font-semibold text-neutral mb-3 flex items-center">
                    {React.cloneElement(ICONS.PENCIL_SQUARE, { className: "w-5 h-5 mr-2 text-ai-creative" })}
                    {t('projectPage.blogPostTitle')}
                </h3>
                {isGeneratingBlogPostLocal ? (
                    <LoadingSpinner message={t('projectPage.generatingBlogPost')} />
                ) : blogPostFile?.content ? (
                    <div className="prose prose-sm max-w-none p-3 bg-ai-creative/5 border border-ai-creative/20 rounded-md shadow-inner">
                         <MarkdownRenderer markdown={blogPostFile.content} />
                    </div>
                ) : (
                    <p className="text-sm text-neutral-500">{t('projectPage.assetGenerator.contentNotYetGenerated', { assetName: t('projectPage.blogPostTitle') })}</p>
                )}
            </section>
        )}

        {shouldShowSection('landing-page') && (
            <section id="landing-page" ref={el => { sectionRefs.current['landing-page'] = el; }} className="pt-20 -mt-20 bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl">
                <h3 className="text-xl font-semibold text-neutral mb-3 flex items-center">
                     {React.cloneElement(ICONS.LINK, { className: "w-5 h-5 mr-2 text-ai-synergy" })}
                     {t('projectPage.landingPage.title')}
                </h3>
                {isGeneratingLandingPageLocal ? (
                    <LoadingSpinner message={t('projectPage.landingPage.generatingButton')} />
                ) : (
                    <>
                        {landingPageDetails.specFile?.content && (
                             <div className="mb-4">
                                <h4 className="text-md font-semibold text-neutral-700 mb-2">{t('projectPage.landingPage.specTitle')}</h4>
                                <div className="prose prose-sm max-w-none p-3 bg-ai-synergy/5 border border-ai-synergy/20 rounded-md shadow-inner max-h-96 overflow-y-auto">
                                    <MarkdownRenderer markdown={landingPageDetails.specFile.content} />
                                </div>
                            </div>
                        )}
                        {landingPageDetails.file?.content && (
                            <div className="mb-4">
                                <h4 className="text-md font-semibold text-neutral-700 mb-2">{t('projectPage.landingPage.previewTitle', { projectName: project.name })}</h4>
                                <div className="border border-neutral-300 rounded-md shadow-inner overflow-hidden">
                                    <iframe
                                        srcDoc={landingPageDetails.content}
                                        title={t('projectPage.landingPage.previewTitle', { projectName: project.name })}
                                        className="w-full h-[500px]"
                                        sandbox="allow-scripts" // Be cautious with sandboxing if scripts are expected
                                    />
                                </div>
                            </div>
                        )}
                        {(!landingPageDetails.file && !landingPageDetails.specFile) && (
                            <p className="text-sm text-neutral-500">{t('projectPage.assetGenerator.contentNotYetGenerated', { assetName: t('projectPage.landingPage.title') })}</p>
                        )}
                    </>
                )}
            </section>
        )}


        <section id="ai-strategic-center" ref={el => { sectionRefs.current['ai-strategic-center'] = el; }} className="pt-20 -mt-20 bg-gradient-to-br from-primary/5 to-accent/5 p-5 sm:p-6 rounded-lg shadow-xl border border-primary/20">
          <h2 className="text-2xl font-semibold text-neutral mb-4 flex items-center">
            {ICONS.AI_SPARKLE} <span className="ml-2">{t('projectPage.aiStrategicCenter')}</span>
          </h2>
          <p className="text-sm text-neutral-600 mb-4">
            {t('projectPage.aiStrategicCenterDescription')}
          </p>
          <button
            onClick={handleRunDeepAnalysis}
            disabled={isAnalyzing || isProjectsHookLoadingGlobal}
            className="bg-accent hover:bg-accent-dark text-base-100 font-semibold inline-flex items-center px-5 py-2.5 text-sm rounded-md shadow-md disabled:opacity-60 transition-colors"
          >
            {isAnalyzing || project.status === 'analyzing_deep' || project.status === 'analyzing_initial' || project.status.startsWith('analyzing_deep_') ? (
              <>
                <LoadingSpinner size="xs" color="text-base-100" containerClassName="flex items-center" />
                <span className="ml-2">{project.detailedStatusMessageKey ? t(project.detailedStatusMessageKey) : t('projectPage.aiAnalyzing')}</span>
              </>
            ) : (
              <>
                {ICONS.LIGHTBULB} <span className="ml-2">{t('projectPage.runDeepAnalysis')}</span>
              </>
            )}
          </button>
          {project.status === 'error' && project.errorDetails && <p className="text-error mt-2 text-sm">{t('projectPage.errorDuringAnalysis', {errorDetails: project.errorDetails})}</p>}
        </section>

        {shouldShowSection('ai-assistant-panel') && (
            <section id="ai-assistant-panel" ref={el => { sectionRefs.current['ai-assistant-panel'] = el; }}  className="pt-20 -mt-20">
                <AIAssistantPanel project={project} />
            </section>
        )}
        
        {project.strategicOutput && (
          <div className="space-y-8">
            {shouldShowSection('project-narrative') && project.strategicOutput.projectPageNarrative && (
              <section id="project-narrative" ref={el => { sectionRefs.current['project-narrative'] = el; }} className="pt-20 -mt-20 bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl">
                <h3 className="text-xl font-semibold text-neutral mb-3 flex items-center">
                  {ICONS.BOOK_OPEN && React.cloneElement(ICONS.BOOK_OPEN, {className: "w-5 h-5 mr-2 text-ai-creative"})} {t('projectPage.projectNarrativeTitle')}
                </h3>
                <div className="prose prose-sm max-w-none p-3 bg-ai-creative/5 border border-ai-creative/20 rounded-md shadow-inner">
                  {project.strategicOutput.projectPageNarrative.split('\n').map((paragraph, index) => (
                    <p key={index}>{paragraph}</p>
                  ))}
                </div>
              </section>
            )}

            {shouldShowSection('generated-image-section') && (
                <div id="generated-image-section" ref={el => { sectionRefs.current['generated-image-section'] = el as HTMLDivElement; }} className="pt-20 -mt-20 bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl space-y-6">
                    {renderImageSection('main', project.strategicOutput.generatedImage, 'projectPage.generatedImageTitle', 'projectPage.imageGeneration.altMainImage', isLoadingMainImage, isLoadingMainImageAlternatives)}
                    {renderImageSection('logo', project.strategicOutput.generatedLogo, 'projectPage.nav.projectLogo', 'projectPage.imageGeneration.altLogo', isLoadingLogo, isLoadingLogoAlternatives)}
                    {/* Sections for Icon and Banner can be added similarly when functionality is ready */}
                </div>
            )}


            {shouldShowSection('knowledge-graph') && project.strategicOutput.knowledgeGraph && (
              <section id="knowledge-graph" ref={el => { sectionRefs.current['knowledge-graph'] = el; }} className="pt-20 -mt-20 bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl">
                <h3 className="text-xl font-semibold text-neutral mb-3 flex items-center">
                   {ICONS.GRAPH && React.cloneElement(ICONS.GRAPH, {className: "w-5 h-5 mr-2 text-ai-technical"})} {t('projectPage.knowledgeGraphTitle')}
                </h3>
                <div className="p-3 bg-ai-technical/5 border border-ai-technical/20 rounded-md shadow-inner">
                  <KnowledgeGraphVisualizer graph={project.strategicOutput.knowledgeGraph} />
                </div>
              </section>
            )}

            {shouldShowSection('strategic-suggestions') && project.strategicOutput.strategicSuggestions && (
              <section id="strategic-suggestions" ref={el => { sectionRefs.current['strategic-suggestions'] = el; }} className="pt-20 -mt-20 bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl">
                <h3 className="text-xl font-semibold text-neutral mb-3 flex items-center">
                  {ICONS.LIGHTBULB && React.cloneElement(ICONS.LIGHTBULB, {className: "w-5 h-5 mr-2 text-ai-next-step"})} {t('projectPage.strategicConsultantTitle')}
                </h3>
                {project.strategicOutput.strategicSuggestions.length > 0 ? (
                  <div className="space-y-4">
                    {project.strategicOutput.strategicSuggestions.map((suggestion: AISuggestion) => (
                      <AISuggestionCard key={suggestion.id} suggestion={suggestion} />
                    ))}
                  </div>
                ) : (
                  <p className="text-neutral-500 text-sm">{t('projectPage.noStrategicSuggestions')}</p>
                )}
              </section>
            )}

             {shouldShowSection('display-template') && project.strategicOutput.suggestedTemplate && (
              <section id="display-template" ref={el => { sectionRefs.current['display-template'] = el; }} className="pt-20 -mt-20 bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl">
                <h3 className="text-xl font-semibold text-neutral mb-3">{t('projectPage.displayTemplateTitle')}</h3>
                 <div className="max-w-xs">
                  <label htmlFor="templateSelect" className="block text-sm font-medium text-neutral-700 mb-1">
                    {t('projectPage.aiSuggested')} <span className="font-semibold text-primary">{t(`projectTemplate_${project.strategicOutput.suggestedTemplate}` as any, project.strategicOutput.suggestedTemplate)}</span>
                  </label>
                  <select
                    id="templateSelect"
                    value={project.selectedTemplate}
                    onChange={handleTemplateChange}
                    className="block w-full pl-3 pr-10 py-2 text-base border-neutral-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md shadow-sm bg-base-100"
                  >
                    {Object.values(ProjectTemplate).map(template => (
                      <option key={template} value={template}>{t(`projectTemplate_${template}` as any, template)}</option>
                    ))}
                  </select>
                  <p className="text-xs text-neutral-500 mt-1">{t('projectPage.templateSelectionNote')}</p>
                </div>
              </section>
            )}
          </div>
        )}
        {!project.strategicOutput && project.status === 'analyzed_initial' && (
          <div className="text-center py-8 text-neutral-500 text-sm">
            <p>{t('projectPage.deepAnalysisPrompt')}</p>
          </div>
        )}
        
        {shouldShowSection('project-files') && (
          <section id="project-files" ref={el => { sectionRefs.current['project-files'] = el; }} className="pt-20 -mt-20 bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl">
            <div className="flex justify-between items-center mb-3">
                <h2 className="text-xl font-semibold text-neutral flex items-center">
                    {ICONS.FOLDER && React.cloneElement(ICONS.FOLDER, {className: "w-5 h-5 mr-2 text-neutral-500"})}
                    {t('projectPage.filesTitle')}
                </h2>
                <button 
                    onClick={handleDownloadAllFiles} 
                    disabled={isZipping || !project.files.some(f => f.content)}
                    className="btn-secondary text-xs px-3 py-1.5 flex items-center disabled:opacity-50"
                >
                    {isZipping ? <LoadingSpinner size="xs"/> : ICONS.DOWNLOAD && React.cloneElement(ICONS.DOWNLOAD, {className:"w-3.5 h-3.5"})}
                    <span className="ml-1.5">{t('projectPage.downloadAllAsZip')}</span>
                </button>
            </div>
            {project.files.length > 0 ? (
              <ul className="space-y-1.5">
                {project.files.map(file => (
                  <li key={file.id} className="text-sm text-neutral-700 flex justify-between items-center p-2 bg-secondary/50 rounded hover:bg-secondary transition-colors group">
                    <span>{file.name} ({file.type}, {Math.round(file.size / 1024)} KB)</span>
                     {file.content && (
                        <a
                            href={file.type === 'text/html' ? URL.createObjectURL(new Blob([file.content], {type: 'text/html'})) : `data:${file.type};charset=utf-8,${encodeURIComponent(file.content)}`}
                            download={file.name}
                            className="text-xs text-primary hover:underline opacity-0 group-hover:opacity-100 transition-opacity px-2 py-1 rounded hover:bg-primary/10"
                            title={t('projectPage.downloadFileTooltip', {fileName: file.name})}
                        >
                            {ICONS.DOWNLOAD && React.cloneElement(ICONS.DOWNLOAD, {className: "w-4 h-4 inline-block mr-1"})}
                            {t('projectPage.downloadButton')}
                        </a>
                     )}
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-neutral-500 text-sm">{t('projectPage.noFiles')}</p>
            )}
          </section>
        )}
      </div>
        <LogoGenerationModal 
            isOpen={isLogoModalOpen}
            onClose={() => setIsLogoModalOpen(false)}
            project={project}
        />
        <AIDeveloperSpecGeneratorModal
            isOpen={isDevSpecModalOpen}
            onClose={() => setIsDevSpecModalOpen(false)}
            project={project}
            onSubmit={async (data) => {
                setIsGeneratingAIDevSpecLocal(true);
                setIsDevSpecModalOpen(false);
                await generateAndSaveAIDevSpec(project.id, data);
                setIsGeneratingAIDevSpecLocal(false);
            }}
            isLoading={isGeneratingAIDevSpecLocal}
        />
        <BlogPostGeneratorModal
            isOpen={isBlogPostModalOpen}
            onClose={() => setIsBlogPostModalOpen(false)}
            project={project}
            onSubmit={async (data) => {
                setIsGeneratingBlogPostLocal(true);
                setIsBlogPostModalOpen(false);
                await generateAndSaveBlogPost(project.id, data);
                setIsGeneratingBlogPostLocal(false);
            }}
            isLoading={isGeneratingBlogPostLocal}
        />
        <LandingPageGeneratorModal
            isOpen={isLandingPageModalOpen}
            onClose={() => setIsLandingPageModalOpen(false)}
            project={project}
            onSubmit={async (data) => {
                setIsGeneratingLandingPageLocal(true);
                setIsLandingPageModalOpen(false);
                await generateAndSaveLandingPage(project.id, data);
                setIsGeneratingLandingPageLocal(false);
            }}
            isLoading={isGeneratingLandingPageLocal}
        />
    </div>
  );
};

export default ProjectPage;
