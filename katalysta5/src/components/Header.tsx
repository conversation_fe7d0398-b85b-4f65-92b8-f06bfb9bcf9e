
import React, { useState, useEffect, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { ICONS } from '../config'; // Updated import

const Header: React.FC = () => {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const mobileMenuRef = useRef<HTMLDivElement>(null);
  const mobileMenuButtonRef = useRef<HTMLButtonElement>(null);

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  // Close mobile menu on route change
  useEffect(() => {
    closeMobileMenu();
  }, [location]);

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isMobileMenuOpen &&
        mobileMenuRef.current &&
        !mobileMenuRef.current.contains(event.target as Node) &&
        mobileMenuButtonRef.current &&
        !mobileMenuButtonRef.current.contains(event.target as Node)
      ) {
        closeMobileMenu();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMobileMenuOpen]);


  const navLinkBaseClasses = "text-neutral hover:text-primary px-2 md:px-3 py-2 rounded-md text-xs sm:text-sm font-medium transition-colors flex items-center";
  const iconLinkClasses = "w-4 h-4 mr-1 md:mr-1.5";

  const mobileNavLinkClasses = "block w-full text-left text-neutral hover:text-primary hover:bg-secondary px-4 py-3 rounded-md text-base font-medium transition-colors flex items-center";

  const navLinks = [
    { to: "/", labelKey: "header.dashboard", icon: null },
    { to: "/new-app-idea", labelKey: "header.newAppIdea", icon: ICONS.WAND, iconColorClass: "text-ai-creative" },
    { to: "/pro-appka", labelKey: "header.proAppka", icon: ICONS.LIGHTBULB, iconColorClass: "text-ai-next-step" },
    { to: "/dynamic-app", labelKey: "header.dynamicApp", icon: ICONS.GRAPH, iconColorClass: "text-ai-technical" },
    { to: "/how-ai-works", labelKey: "header.aiCapabilities", icon: null },
  ];

  return (
    <header className="bg-base-100 sticky top-0 w-full z-50 shadow-md">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <Link to="/" className="flex items-center space-x-2 text-xl sm:text-2xl font-bold text-primary hover:opacity-80 transition-opacity" onClick={closeMobileMenu}>
            {ICONS.AI_SPARKLE && React.cloneElement(ICONS.AI_SPARKLE, {className: "w-6 h-6 sm:w-7 sm:h-7 text-accent flex-shrink-0"})}
            <span className="truncate max-w-[120px] xs:max-w-[150px] sm:max-w-xs md:max-w-md lg:max-w-lg">{t('appName')}</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden sm:flex space-x-0.5 md:space-x-1">
            {navLinks.map(link => (
              <Link key={link.to} to={link.to} className={navLinkBaseClasses}>
                {link.icon && React.cloneElement(link.icon, { className: `${iconLinkClasses} ${link.iconColorClass || ''}` })}
                {t(link.labelKey)}
              </Link>
            ))}
          </nav>

          <div className="flex items-center">
            {/* Mobile Menu Button */}
            <div className="sm:hidden ml-2">
                 <button
                    ref={mobileMenuButtonRef}
                    onClick={toggleMobileMenu}
                    className="p-2 text-neutral hover:text-primary focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary rounded-md"
                    aria-label={t('common.openMenu')}
                    aria-expanded={isMobileMenuOpen}
                    aria-controls="mobile-menu-nav"
                    >
                    {isMobileMenuOpen
                        ? (ICONS.X_MARK && React.cloneElement(ICONS.X_MARK, {className: "w-6 h-6"}))
                        : (ICONS.MENU && React.cloneElement(ICONS.MENU, {className: "w-6 h-6"}))
                    }
                </button>
            </div>

            {/* Language Switcher */}
            <div className="ml-2 sm:ml-3 flex items-center border-l border-neutral-300 pl-2 sm:pl-3">
              <button
                onClick={() => changeLanguage('sk')}
                className={`px-2 py-1 text-xs rounded transition-colors ${i18n.language.startsWith('sk') ? 'bg-primary text-base-100 font-semibold' : 'bg-secondary text-neutral hover:bg-neutral-30/50'}`}
                aria-pressed={i18n.language.startsWith('sk')}
                lang="sk"
              >
                SK
              </button>
              <button
                onClick={() => changeLanguage('en')}
                className={`ml-1 px-2 py-1 text-xs rounded transition-colors ${i18n.language.startsWith('en') ? 'bg-primary text-base-100 font-semibold' : 'bg-secondary text-neutral hover:bg-neutral-30/50'}`}
                aria-pressed={i18n.language.startsWith('en')}
                lang="en"
              >
                EN
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Menu Panel */}
      {isMobileMenuOpen && (
        <div
          id="mobile-menu-nav"
          ref={mobileMenuRef}
          className="sm:hidden absolute top-16 left-0 right-0 bg-base-100 shadow-lg z-40 border-t border-neutral-200"
          role="navigation"
          aria-label="Mobile navigation"
        >
          <div className="px-2 pt-2 pb-3 space-y-1">
            {navLinks.map(link => (
              <Link
                key={`mobile-${link.to}`}
                to={link.to}
                className={mobileNavLinkClasses}
                onClick={closeMobileMenu} // Close menu on link click
              >
                {link.icon && React.cloneElement(link.icon, { className: `w-5 h-5 mr-3 ${link.iconColorClass || ''}` })}
                {t(link.labelKey)}
              </Link>
            ))}
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
