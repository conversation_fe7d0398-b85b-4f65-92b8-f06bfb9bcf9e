
import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useProjects } from '../hooks/useProjects';
import { useNotifications } from '../contexts/NotificationContext'; // Added
import ProjectCard from './ProjectCard';
import FileUpload from './FileUpload';
import LoadingSpinner from './LoadingSpinner';
import QuickViewModal from './QuickViewModal';
import ProjectFilterPanel from './ProjectFilterPanel';
import { ICONS } from '../config'; // Updated import
import { ProjectFile, PortfolioAnalysis, Project } from '../types';

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { showNotification } = useNotifications(); // Added
  const { projects, addProject, isLoading: isGlobalLoading, portfolioAnalysis, isLoadingPortfolio, analyzePortfolio } = useProjects();

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newProjectName, setNewProjectName] = useState('');
  const [newProjectDescription, setNewProjectDescription] = useState('');
  const [newProjectFiles, setNewProjectFiles] = useState<ProjectFile[]>([]);

  const [selectedProjectForQuickView, setSelectedProjectForQuickView] = useState<Project | null>(null);
  const [filters, setFilters] = useState({ status: '', favorite: 'all' as 'all' | 'yes' | 'no', searchTerm: '' });
  const [sortBy, setSortBy] = useState('lastInteractionTimestamp_desc'); // Default to recently active
  const [highlightedProjectIds, setHighlightedProjectIds] = useState<{ids: string[], type: 'synergy' | 'conflict' | 'sharedResource' | null}>({ids: [], type: null});

  useEffect(() => {
    if (projects.length > 0 && !portfolioAnalysis && !isLoadingPortfolio) {
      analyzePortfolio();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projects.length, portfolioAnalysis, isLoadingPortfolio]);

  const handleCreateProject = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newProjectName.trim() || !newProjectDescription.trim()) {
      showNotification({ type: 'warning', title: t('notifications.inputRequiredTitle'), message: t('createProjectModal.errorNameDescriptionRequired')});
      return;
    }
    const newProjectId = await addProject(newProjectName, newProjectDescription, newProjectFiles);
    if (newProjectId) {
      setNewProjectName('');
      setNewProjectDescription('');
      setNewProjectFiles([]);
      setShowCreateModal(false);
      showNotification({type: 'success', title: t('notifications.projectCreatedSuccessTitle'), message: t('notifications.projectCreatedSuccessMessage', {projectName: newProjectName})});
    } else {
      // Error notification is handled within useProjects if addProject fails due to API key etc.
      // If a different kind of failure specific to this form, add here:
      // showNotification({ type: 'error', title: t('notifications.creationFailedTitle'), message: t('notifications.projectCreationFailedMessage')});
    }
  };

  const handleFilterChange = (filterName: keyof typeof filters, value: string) => {
    setFilters(prev => ({ ...prev, [filterName]: value }));
    if (highlightedProjectIds.ids.length > 0) setHighlightedProjectIds({ ids: [], type: null });
  };

  const handleSortChange = (newSortBy: string) => {
    setSortBy(newSortBy);
     if (highlightedProjectIds.ids.length > 0) setHighlightedProjectIds({ ids: [], type: null });
  }


  const projectStatuses = useMemo(() => {
    const statuses = new Set(projects.map(p => p.status));
    return Array.from(statuses).sort();
  }, [projects]);

  const filteredAndSortedProjects = useMemo(() => {
    let tempProjects = [...projects];

    if (filters.status) {
      tempProjects = tempProjects.filter(p => p.status === filters.status);
    }
    if (filters.favorite === 'yes') {
      tempProjects = tempProjects.filter(p => p.isFavorite);
    } else if (filters.favorite === 'no') {
      tempProjects = tempProjects.filter(p => !p.isFavorite);
    }
    if (filters.searchTerm) {
      const searchTermLower = filters.searchTerm.toLowerCase();
      tempProjects = tempProjects.filter(p =>
        p.name.toLowerCase().includes(searchTermLower) ||
        p.description.toLowerCase().includes(searchTermLower) ||
        (p.initialAnalysis?.keywords?.some(k => k.toLowerCase().includes(searchTermLower))) ||
        (p.initialAnalysis?.projectTypeGuess?.toLowerCase().includes(searchTermLower))
      );
    }

    if (highlightedProjectIds.type && highlightedProjectIds.ids.length > 0) {
        tempProjects = tempProjects.filter(p => highlightedProjectIds.ids.includes(p.id));
    }

    tempProjects.sort((a, b) => {
      switch (sortBy) {
        case 'createdAt_asc': return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case 'name_asc': return a.name.localeCompare(b.name);
        case 'name_desc': return b.name.localeCompare(a.name);
        case 'updatedAt_desc':
        case 'lastInteractionTimestamp_desc':
        default:
          return new Date(b.lastInteractionTimestamp || b.updatedAt).getTime() - new Date(a.lastInteractionTimestamp || a.updatedAt).getTime();
        case 'createdAt_desc':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      }
    });
    return tempProjects;
  }, [projects, filters, sortBy, highlightedProjectIds]);

  const handlePortfolioItemClick = (itemProjectIds: string[], type: 'synergy' | 'conflict' | 'sharedResource') => {
    const sortedItemProjectIds = [...itemProjectIds].sort();
    const sortedCurrentHighlightedIds = [...highlightedProjectIds.ids].sort();

    if (highlightedProjectIds.type === type && JSON.stringify(sortedCurrentHighlightedIds) === JSON.stringify(sortedItemProjectIds)) {
        setHighlightedProjectIds({ ids: [], type: null });
    } else {
        setHighlightedProjectIds({ ids: itemProjectIds, type });
    }
  };

  const renderPortfolioAnalysis = (analysis: PortfolioAnalysis | null) => {
    if (isLoadingPortfolio) return <div className="min-h-[100px] flex items-center justify-center"><LoadingSpinner message={t('dashboard.analyzing')} /></div>;
    if (!analysis) return <p className="text-neutral-70">{t('dashboard.aiPortfolioOverview.noAnalysisYet')}</p>;

    const hasSynergies = analysis.synergies && analysis.synergies.length > 0;
    const hasConflicts = analysis.conflicts && analysis.conflicts.length > 0;
    const hasSharedResources = analysis.sharedResources && analysis.sharedResources.length > 0;

    if (analysis.conflicts && analysis.conflicts.length > 0 && analysis.conflicts[0].projectIds?.length === 0 && analysis.conflicts[0].description.toLowerCase().includes('failed')) {
      return <p className="text-status-error">{t('dashboard.portfolioAnalysisFailed', { error: analysis.conflicts[0].description })}</p>;
    }

    if (!hasSynergies && !hasConflicts && !hasSharedResources) {
      return <p className="text-neutral-70">{t('dashboard.aiPortfolioOverview.noInteractions')}</p>;
    }

    const renderListItem = (item: {projectIds: string[], description: string}, type: 'synergy' | 'conflict' | 'sharedResource', resourceName?: string) => {
        const isHighlighted = highlightedProjectIds.type === type &&
                              JSON.stringify([...highlightedProjectIds.ids].sort()) === JSON.stringify([...item.projectIds].sort());
        const projectNames = item.projectIds.map(id => projects.find(p=>p.id===id)?.name || id).join(', ');

        return (
            <li key={`${type}-${item.projectIds.join('-')}-${resourceName || ''}`}>
                <button
                    onClick={() => handlePortfolioItemClick(item.projectIds, type)}
                    className={`w-full text-left p-1.5 rounded transition-all duration-200 ease-in-out focus:outline-none focus-visible:ring-2 focus-visible:ring-accent
                                ${isHighlighted ?
                                    (type === 'synergy' ? 'bg-ai-synergy/10 text-ai-synergy font-semibold shadow-sm ring-1 ring-ai-synergy/30' :
                                     type === 'conflict' ? 'bg-ai-risk-high/10 text-ai-risk-high font-semibold shadow-sm ring-1 ring-ai-risk-high/30' :
                                     'bg-ai-opportunity-medium/10 text-ai-opportunity-medium font-semibold shadow-sm ring-1 ring-ai-opportunity-medium/30')
                                    : 'hover:bg-secondary'
                                }`}
                >
                    {resourceName && <span className="font-semibold">{resourceName}: </span>}
                    {item.description}
                    <span className="text-neutral-500 text-xs block mt-0.5"> ({t('dashboard.aiPortfolioOverview.projectsAffected', { ids: projectNames })})</span>
                </button>
            </li>
        );
    };

    return (
      <div className="space-y-3 text-sm">
        {hasSynergies && (
          <div>
            <h3 className="text-sm font-semibold text-status-success flex items-center">{ICONS.GRAPH && React.cloneElement(ICONS.GRAPH, {className: "w-4 h-4 mr-1"})} {t('dashboard.aiPortfolioOverview.synergies')}</h3>
            <ul className="list-none pl-0 space-y-1 mt-1">
              {analysis.synergies.map(s => renderListItem(s, 'synergy'))}
            </ul>
          </div>
        )}
        {hasConflicts && (
          <div>
            <h3 className="text-sm font-semibold text-status-error flex items-center">{ICONS.EXCLAMATION_TRIANGLE && React.cloneElement(ICONS.EXCLAMATION_TRIANGLE, {className: "w-4 h-4 mr-1"})} {t('dashboard.aiPortfolioOverview.conflicts')}</h3>
            <ul className="list-none pl-0 space-y-1 mt-1">
              {analysis.conflicts.map(c => renderListItem(c, 'conflict'))}
            </ul>
          </div>
        )}
        {hasSharedResources && (
          <div>
            <h3 className="text-sm font-semibold text-ai-research flex items-center">{ICONS.PROJECT && React.cloneElement(ICONS.PROJECT, {className: "w-4 h-4 mr-1"})} {t('dashboard.aiPortfolioOverview.sharedResources')}</h3>
             <ul className="list-none pl-0 space-y-1 mt-1">
              {analysis.sharedResources.map(r => renderListItem(r, 'sharedResource', r.resource))}
            </ul>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-3xl font-bold text-neutral">{t('dashboard.title')}</h1>
        <button
          onClick={() => setShowCreateModal(true)}
          className="btn-primary inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary hover:shadow-lg transition-shadow"
          aria-haspopup="dialog"
        >
          {ICONS.ADD}
          <span className="ml-2">{t('dashboard.newProjectButton')}</span>
        </button>
      </div>

      {isGlobalLoading && projects.length === 0 && <LoadingSpinner message={t('createProjectModal.processingNewProject')} />}

      {projects.length > 0 && (
        <>
          <ProjectFilterPanel
            filters={filters}
            onFilterChange={handleFilterChange}
            sortBy={sortBy}
            onSortChange={handleSortChange}
            projectStatuses={projectStatuses}
          />
          <div className="bg-base-100 p-4 sm:p-6 rounded-lg shadow-lg">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 gap-2">
              <h2 className="text-lg sm:text-xl font-semibold text-neutral flex items-center">
                {React.cloneElement(ICONS.AI_SPARKLE, {className: "text-accent w-5 h-5 sm:w-6 sm:h-6"})} <span className="ml-2">{t('dashboard.aiPortfolioOverview.title')}</span>
              </h2>
              <button
                onClick={analyzePortfolio}
                disabled={isLoadingPortfolio || projects.length < 2}
                className="text-xs sm:text-sm text-primary hover:underline disabled:opacity-50 disabled:cursor-not-allowed flex items-center self-start sm:self-center"
              >
                {isLoadingPortfolio ? t('dashboard.analyzing') : (<>{ICONS.REFRESH && React.cloneElement(ICONS.REFRESH, {className: "w-4 h-4 mr-1"})} {t('dashboard.aiPortfolioOverview.refreshAnalysis')}</>)}
              </button>
            </div>
            {renderPortfolioAnalysis(portfolioAnalysis)}
          </div>
        </>
      )}

      {projects.length === 0 && !isGlobalLoading && (
        <div className="text-center py-12 bg-base-100 rounded-lg shadow-xl mt-8 border border-primary/20">
            <div className="inline-block p-5 bg-gradient-to-br from-primary/10 to-accent/10 rounded-full mb-5 transform transition-transform hover:scale-110">
                {React.cloneElement(ICONS.AI_SPARKLE, {className: "w-16 h-16 text-primary opacity-90"})}
            </div>
          <h2 className="mt-2 text-2xl font-semibold text-neutral">{t('dashboard.emptyState.title')}</h2>
          <p className="mt-1 text-neutral-70">{t('dashboard.emptyState.description')}</p>
          <button
            onClick={() => setShowCreateModal(true)}
            className="mt-6 btn-primary inline-flex items-center px-6 py-3 text-base font-medium rounded-md shadow-lg hover:shadow-xl transform hover:scale-105 transition-all focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            aria-haspopup="dialog"
            >
            {ICONS.ADD && React.cloneElement(ICONS.ADD, {className:"w-5 h-5"})}
            <span className="ml-2">{t('dashboard.createProjectButton')}</span>
        </button>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredAndSortedProjects.map(project => {
           let cardHighlightType: 'synergy' | 'conflict' | 'sharedResource' | null = null;
           if (highlightedProjectIds.type && highlightedProjectIds.ids.includes(project.id)) {
               cardHighlightType = highlightedProjectIds.type;
           }
           return (
            <ProjectCard
                key={project.id}
                project={project}
                onShowQuickView={setSelectedProjectForQuickView}
                portfolioInteraction={cardHighlightType}
            />);
        })}
      </div>

      {showCreateModal && (
        <div
            className="fixed inset-0 bg-neutral/60 backdrop-blur-sm flex items-center justify-center z-[1000] p-4 opacity-100"
            onClick={() => setShowCreateModal(false)}
            role="dialog"
            aria-modal="true"
            aria-labelledby="create-project-modal-title"
        >
          <div
            className="bg-base-100 p-6 sm:p-8 rounded-xl shadow-2xl w-full max-w-lg max-h-[90vh] overflow-y-auto opacity-100 transform transition-all duration-300 ease-out scale-100"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center mb-6">
                <h2 id="create-project-modal-title" className="text-2xl font-semibold text-neutral">{t('createProjectModal.title')}</h2>
                <button onClick={() => setShowCreateModal(false)} className="p-1 text-neutral-500 hover:text-error rounded-full focus-visible:ring-2 focus-visible:ring-error" aria-label={t('common.close')}>
                    {ICONS.X_MARK && React.cloneElement(ICONS.X_MARK, {className: "w-6 h-6"})}
                </button>
            </div>
            <form onSubmit={handleCreateProject} className="space-y-4">
              <div>
                <label htmlFor="projectName" className="block text-sm font-medium text-neutral-70">{t('createProjectModal.projectNameLabel')}</label>
                <input
                  type="text"
                  id="projectName"
                  value={newProjectName}
                  onChange={(e) => setNewProjectName(e.target.value)}
                  className="mt-1 block w-full px-3 py-2 border border-neutral-30 rounded-md shadow-sm focus:ring-2 focus:ring-primary focus:border-primary sm:text-sm"
                  required
                  placeholder={t('createProjectModal.projectNamePlaceholder')}
                />
              </div>
              <div>
                <label htmlFor="projectDescription" className="block text-sm font-medium text-neutral-70">{t('createProjectModal.projectDescriptionLabel')}</label>
                <textarea
                  id="projectDescription"
                  value={newProjectDescription}
                  onChange={(e) => setNewProjectDescription(e.target.value)}
                  rows={3}
                  className="mt-1 block w-full px-3 py-2 border border-neutral-30 rounded-md shadow-sm focus:ring-2 focus:ring-primary focus:border-primary sm:text-sm"
                  required
                  placeholder={t('createProjectModal.projectDescriptionPlaceholder')}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-neutral-70 mb-1">{t('createProjectModal.uploadFilesLabel')}</label>
                 <FileUpload onFilesUploaded={setNewProjectFiles} />
              </div>
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowCreateModal(false)}
                  className="btn-secondary px-4 py-2 text-sm font-medium rounded-md"
                >
                  {t('createProjectModal.cancelButton')}
                </button>
                <button
                  type="submit"
                  disabled={isGlobalLoading && showCreateModal}
                  className="btn-primary inline-flex items-center px-4 py-2 text-sm font-medium rounded-md shadow-sm disabled:opacity-50"
                >
                  {(isGlobalLoading && showCreateModal) ? <LoadingSpinner size="xs" color="text-base-100" containerClassName='flex items-center' /> : <>{ICONS.AI_SPARKLE && React.cloneElement(ICONS.AI_SPARKLE, {className:"w-4 h-4"})} <span className="ml-2">{t('createProjectModal.createAndAnalyzeButton')}</span></>}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
      <QuickViewModal
        project={selectedProjectForQuickView}
        onClose={() => setSelectedProjectForQuickView(null)}
        onGoToProject={(projectId) => {
            setSelectedProjectForQuickView(null);
            navigate(`/project/${projectId}`);
        }}
      />
    </div>
  );
};

export default Dashboard;
