import { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useProjects } from '../../../hooks/useProjects';
import { useNotifications } from '../../../contexts/NotificationContext';
import { Project, GeneratedImageDetails } from '../../../types';
import { geminiService } from '../../../services/geminiService';

export const useProjectPageImages = (project: Project | null) => {
  const { t, i18n } = useTranslation();
  const { showNotification } = useNotifications();
  const { updateProjectGeneratedImage } = useProjects();

  const [showMainImagePrompt, setShowMainImagePrompt] = useState(false);
  const [isLoadingMainImage, setIsLoadingMainImage] = useState(false);
  const [isLoadingMainImageAlternatives, setIsLoadingMainImageAlternatives] = useState(false);
  const [isLoadingLogo, setIsLoadingLogo] = useState(false);
  const [isLoadingLogoAlternatives, setIsLoadingLogoAlternatives] = useState(false);
  // Placeholder for icon and banner states if needed in future
  // const [isLoadingIcon, setIsLoadingIcon] = useState(false);
  // const [isLoadingBanner, setIsLoadingBanner] = useState(false);

  const toggleShowMainImagePrompt = useCallback(() => {
    setShowMainImagePrompt(prev => !prev);
  }, []);

  const handleGenerateImage = useCallback(async (imageType: 'main' | 'logo' | 'icon' | 'banner', existingPrompt?: string) => {
    if (!project) return;
    const currentLang = i18n.language;

    const setLoadingState = (loading: boolean) => {
      switch (imageType) {
        case 'main': setIsLoadingMainImage(loading); break;
        case 'logo': setIsLoadingLogo(loading); break;
        // case 'icon': setIsLoadingIcon(loading); break;
        // case 'banner': setIsLoadingBanner(loading); break;
      }
    };
    setLoadingState(true);
    const assetTranslationKey = `projectPage.nav.${imageType === 'main' ? 'imageSection' : imageType === 'logo' ? 'projectLogo' : imageType === 'icon' ? 'projectIcon' : 'projectBanner'}`;
    const assetName = t(assetTranslationKey);
    showNotification({ type: 'info', title: t('projectPage.imageGeneration.generating', { asset: assetName }), message: t('projectPage.imageGeneration.generating', { asset: assetName }) });

    try {
      let imageDetails: GeneratedImageDetails | undefined;
      if (imageType === 'main') {
        imageDetails = await geminiService.generateProjectImage(project, currentLang);
      } else if (imageType === 'logo') {
        if (project.dataCore.logoBrief) {
          imageDetails = await geminiService.generateLogoFromBrief(project.dataCore.logoBrief, project, currentLang);
        } else {
          imageDetails = await geminiService.generateLogo(project, currentLang); // Generic logo
        }
      }
      // Placeholder for icon/banner generation

      if (imageDetails?.url) {
        updateProjectGeneratedImage(project.id, imageType, imageDetails);
        showNotification({ type: 'success', title: t('notifications.generationSuccessTitle'), message: t('notifications.imageGeneratedSuccess', { assetName }) });
      } else {
        throw new Error(t('projectPage.imageGeneration.generationFailed', { asset: assetName }));
      }
    } catch (error: any) {
      console.error(`Error generating ${imageType} image:`, error);
      showNotification({ type: 'error', title: t('notifications.generationFailedTitle'), message: t('projectPage.imageGeneration.generationFailedError', { asset: assetName, error: error.message }) });
    } finally {
      setLoadingState(false);
    }
  }, [project, updateProjectGeneratedImage, showNotification, t, i18n.language]);

  const handleGenerateImageAlternatives = useCallback(async (imageType: 'main' | 'logo' | 'icon' | 'banner') => {
    if (!project) return;
    const currentLang = i18n.language;
    const existingImageDetails = imageType === 'main' ? project.strategicOutput?.generatedImage :
                               imageType === 'logo' ? project.strategicOutput?.generatedLogo :
                               // imageType === 'icon' ? project.strategicOutput?.generatedIcon :
                               project.strategicOutput?.generatedBanner;

    if (!existingImageDetails?.prompt) {
      showNotification({ type: 'warning', title: t('notifications.noBasePromptTitle'), message: t('projectPage.imageGeneration.noBasePrompt', { asset: imageType }) });
      return;
    }
    const setLoadingAlternativesState = (loading: boolean) => {
      switch (imageType) {
        case 'main': setIsLoadingMainImageAlternatives(loading); break;
        case 'logo': setIsLoadingLogoAlternatives(loading); break;
        // case 'icon': setIsLoadingIconAlternatives(loading); break;
        // case 'banner': setIsLoadingBannerAlternatives(loading); break;
      }
    };
    setLoadingAlternativesState(true);
    const assetName = t(`projectPage.nav.${imageType === 'main' ? 'imageSection' : imageType === 'logo' ? 'projectLogo' : imageType === 'icon' ? 'projectIcon' : 'projectBanner'}`);
    showNotification({ type: 'info', title: t('projectPage.imageGeneration.generatingAlternatives', { asset: assetName }), message: t('projectPage.imageGeneration.generatingAlternatives', { asset: assetName }) });

    try {
      const alternatives = await geminiService.generateImageAlternatives(existingImageDetails.prompt, currentLang, 3);
      if (alternatives.length > 0) {
        updateProjectGeneratedImage(project.id, imageType, { ...existingImageDetails, alternatives });
        showNotification({ type: 'success', title: t('notifications.generationSuccessTitle'), message: t('notifications.alternativesGeneratedSuccess', { assetName }) });
      } else {
        showNotification({ type: 'info', title: t('notifications.noAlternativesFoundTitle'), message: t('projectPage.imageGeneration.noAlternatives', { asset: assetName }) });
      }
    } catch (error: any) {
      console.error(`Error generating ${imageType} alternatives:`, error);
      showNotification({ type: 'error', title: t('notifications.generationFailedTitle'), message: error.message });
    } finally {
      setLoadingAlternativesState(false);
    }
  }, [project, updateProjectGeneratedImage, showNotification, t, i18n.language]);

  const handleSelectAlternative = useCallback((imageType: 'main' | 'logo' | 'icon' | 'banner', alternativeUrl: string) => {
    if (!project) return;
    const existingImageDetails = imageType === 'main' ? project.strategicOutput?.generatedImage :
                               imageType === 'logo' ? project.strategicOutput?.generatedLogo :
                               // imageType === 'icon' ? project.strategicOutput?.generatedIcon :
                               project.strategicOutput?.generatedBanner;
    if (existingImageDetails) {
      updateProjectGeneratedImage(project.id, imageType, { ...existingImageDetails, url: alternativeUrl, alternatives: [] });
      showNotification({ type: 'success', title: t('notifications.updateSuccessTitle'), message: t('notifications.imageAlternativeSelected') });
    }
  }, [project, updateProjectGeneratedImage, showNotification, t]);

  return {
    showMainImagePrompt,
    isLoadingMainImage,
    isLoadingMainImageAlternatives,
    isLoadingLogo,
    isLoadingLogoAlternatives,
    // isLoadingIcon,
    // isLoadingBanner,
    toggleShowMainImagePrompt,
    handleGenerateImage,
    handleGenerateImageAlternatives,
    handleSelectAlternative,
  };
};
