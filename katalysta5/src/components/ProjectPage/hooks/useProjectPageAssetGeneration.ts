import { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import J<PERSON><PERSON><PERSON> from 'jszip';
import { useProjects } from '../../../hooks/useProjects';
import { useNotifications } from '../../../contexts/NotificationContext';
import { Project, AIDevSpecFormData, BlogPostFormData, LandingPageFormData } from '../../../types';
import { geminiService } from '../../../services/geminiService';

export const useProjectPageAssetGeneration = (project: Project | null) => {
  const { t, i18n } = useTranslation();
  const { showNotification } = useNotifications();
  const { 
    addConceptualFileToProject,
    generateAndSaveAIDevSpec: generateAndSaveAIDevSpecContext,
    generateAndSaveBlogPost: generateAndSaveBlogPostContext,
    generateAndSaveLandingPage: generateAndSaveLandingPageContext,
  } = useProjects();

  const [isGeneratingAIDevSpecLocal, setIsGeneratingAIDevSpecLocal] = useState(false);
  const [isGeneratingBlogPostLocal, setIsGeneratingBlogPostLocal] = useState(false);
  const [isGeneratingLandingPageLocal, setIsGeneratingLandingPageLocal] = useState(false);
  const [isGeneratingDocumentationLocal, setIsGeneratingDocumentationLocal] = useState(false);
  const [isZipping, setIsZipping] = useState(false);

  const handleGenerateDocumentation = useCallback(async (blueprintContent: string | undefined) => {
    if (!project || !blueprintContent) {
      showNotification({ type: 'warning', title: t('notifications.inputRequiredTitle'), message: t('projectPage.deepAnalysisRequiredForFeature') });
      return;
    }
    showNotification({ type: 'info', title: t('projectPage.generatingDocumentation'), message: t('projectPage.generatingDocumentation') });
    setIsGeneratingDocumentationLocal(true);
    try {
      const outline = await geminiService.generateDocumentationOutline(blueprintContent, i18n.language);
      const outlineFileName = t('projectPage.documentationOutlineFileName', { projectName: project.name, defaultValue: `${project.name}_Documentation_Outline.md` });
      addConceptualFileToProject(project.id, outlineFileName, 'text/markdown', outline.length, outline);
      showNotification({ type: 'success', title: t('notifications.generationSuccessTitle'), message: t('projectPage.documentationOutlineGenerated') });
    } catch (error: any) {
      showNotification({ type: 'error', title: t('notifications.generationFailedTitle'), message: error.message || t('projectPage.generateDocumentationError') });
    } finally {
      setIsGeneratingDocumentationLocal(false);
    }
  }, [project, addConceptualFileToProject, showNotification, t, i18n.language]);

  const handleDownloadAllFiles = useCallback(async () => {
    if (!project || !project.files || project.files.length === 0) {
      showNotification({ type: 'info', message: t('projectPage.noFilesToZip') });
      return;
    }
    // Filter for files that actually have content or known image URLs that can be fetched.
    const filesWithContentOrKnownSource = project.files.filter(file => 
        file.content || 
        (file.type.startsWith('image/') && (
            (project.strategicOutput?.generatedImage?.url && file.name === "project_main_image.jpg") ||
            (project.strategicOutput?.generatedLogo?.url && file.name === "project_logo.jpg") ||
            (project.strategicOutput?.generatedIcon?.url && file.name === "project_icon.jpg") ||
            (project.strategicOutput?.generatedBanner?.url && file.name === "project_banner.jpg")
        ))
    );

    if (filesWithContentOrKnownSource.length === 0) {
      showNotification({ type: 'info', message: t('projectPage.noContentToZip') });
      return;
    }
    
    setIsZipping(true);
    showNotification({ type: 'info', title: t('projectPage.generatingZip'), message: t('projectPage.generatingZipMessage') });

    try {
      const zip = new JSZip();
      for (const file of filesWithContentOrKnownSource) { // Iterate over filtered list
        if (file.content) {
          zip.file(file.name, file.content);
        } else if (file.type.startsWith('image/')) {
          let imageUrlToFetch: string | undefined = undefined;
          if (project.strategicOutput?.generatedImage?.url && file.name === "project_main_image.jpg") imageUrlToFetch = project.strategicOutput.generatedImage.url;
          else if (project.strategicOutput?.generatedLogo?.url && file.name === "project_logo.jpg") imageUrlToFetch = project.strategicOutput.generatedLogo.url;
          else if (project.strategicOutput?.generatedIcon?.url && file.name === "project_icon.jpg") imageUrlToFetch = project.strategicOutput.generatedIcon.url;
          else if (project.strategicOutput?.generatedBanner?.url && file.name === "project_banner.jpg") imageUrlToFetch = project.strategicOutput.generatedBanner.url;

          if (imageUrlToFetch && imageUrlToFetch.startsWith('data:image')) {
            const base64Data = imageUrlToFetch.split(',')[1];
            zip.file(file.name, base64Data, { base64: true });
          }
        }
      }
      const zipBlob = await zip.generateAsync({ type: 'blob' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(zipBlob);
      const zipFileName = `${project.name.replace(/[^a-z0-9]/gi, '_')}_AI_Catalyst_Files.zip`;
      link.download = zipFileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href); // Clean up the object URL
      showNotification({ type: 'success', title: t('notifications.downloadSuccessTitle'), message: t('notifications.zipGeneratedMessage', { fileName: zipFileName }) });
    } catch (error) {
      console.error("Error zipping files:", error);
      showNotification({ type: 'error', title: t('notifications.zipErrorTitle'), message: t('projectPage.zipError') });
    } finally {
      setIsZipping(false);
    }
  }, [project, showNotification, t]);

  const handleDevSpecSubmit = useCallback(async (data: AIDevSpecFormData) => {
    if (!project) return;
    setIsGeneratingAIDevSpecLocal(true);
    await generateAndSaveAIDevSpecContext(project.id, data);
    setIsGeneratingAIDevSpecLocal(false);
  }, [project, generateAndSaveAIDevSpecContext]);

  const handleBlogPostSubmit = useCallback(async (data: BlogPostFormData) => {
    if (!project) return;
    setIsGeneratingBlogPostLocal(true);
    await generateAndSaveBlogPostContext(project.id, data);
    setIsGeneratingBlogPostLocal(false);
  }, [project, generateAndSaveBlogPostContext]);

  const handleLandingPageSubmit = useCallback(async (data: LandingPageFormData) => {
    if (!project) return;
    setIsGeneratingLandingPageLocal(true);
    await generateAndSaveLandingPageContext(project.id, data);
    setIsGeneratingLandingPageLocal(false);
  }, [project, generateAndSaveLandingPageContext]);

  return {
    isGeneratingAIDevSpecLocal,
    isGeneratingBlogPostLocal,
    isGeneratingLandingPageLocal,
    isGeneratingDocumentationLocal,
    isZipping,
    handleGenerateDocumentation,
    handleDownloadAllFiles,
    handleDevSpecSubmit,
    handleBlogPostSubmit,
    handleLandingPageSubmit,
  };
};
