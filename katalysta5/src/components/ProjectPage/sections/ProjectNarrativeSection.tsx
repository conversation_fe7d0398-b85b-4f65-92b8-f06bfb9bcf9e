
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Project } from '../../../types';
import { ICONS } from '../../../config';
import { ProjectPageSectionProps } from '../ProjectPage.types'; // sectionRef removed from this type

const ProjectNarrativeSection: React.FC<ProjectPageSectionProps> = ({ project }) => { // No longer Omit
  const { t } = useTranslation();

  if (!project.strategicOutput?.projectPageNarrative) return null;

  return (
    <section id="project-narrative" className="bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl"> {/* Removed pt-20 -mt-20 */}
      <h3 className="text-xl font-semibold text-neutral mb-3 flex items-center">
        {ICONS.BOOK_OPEN && React.cloneElement(ICONS.BOOK_OPEN, { className: "w-5 h-5 mr-2 text-ai-creative" })} {t('projectPage.projectNarrativeTitle')}
      </h3>
      <div className="prose prose-sm max-w-none p-3 bg-ai-creative/5 border border-ai-creative/20 rounded-md shadow-inner">
        {project.strategicOutput.projectPageNarrative.split('\n').map((paragraph, index) => (
          <p key={index}>{paragraph}</p>
        ))}
      </div>
    </section>
  );
};

export default ProjectNarrativeSection;
