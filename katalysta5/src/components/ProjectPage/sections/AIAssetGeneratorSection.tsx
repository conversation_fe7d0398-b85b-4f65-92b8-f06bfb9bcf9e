
import React from 'react';
import { useTranslation } from 'react-i18next';
import { ICONS } from '../../../config';
import { ProjectPageSectionProps } from '../ProjectPage.types'; // sectionRef removed from this type

interface AIAssetGeneratorSectionProps extends ProjectPageSectionProps { // No longer Omit
  onOpenLogoModal: () => void;
  onOpenDevSpecModal: () => void;
  onOpenBlogPostModal: () => void;
  onOpenLandingPageModal: () => void;
  isAnyAssetGeneratorLoading: boolean;
}

const AIAssetGeneratorSection: React.FC<AIAssetGeneratorSectionProps> = ({
  onOpenLogoModal,
  onOpenDevSpecModal,
  onOpenBlogPostModal,
  onOpenLandingPageModal,
  isAnyAssetGeneratorLoading,
}) => {
  const { t } = useTranslation();

  return (
    <section id="ai-asset-generator" className="bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl"> {/* Removed pt-20 -mt-20 */}
      <h3 className="text-xl font-semibold text-neutral mb-3 flex items-center">
        {React.cloneElement(ICONS.AI_SPARKLE, { className: "w-6 h-6 mr-2 text-accent" })}
        {t('projectPage.assetGenerator.title')}
      </h3>
      <p className="text-sm text-neutral-600 mb-4">{t('projectPage.assetGenerator.description')}</p>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-3 sm:gap-4">
        <button onClick={onOpenLogoModal} className="btn-secondary w-full flex items-center justify-center p-2.5 text-sm" disabled={isAnyAssetGeneratorLoading}>
          {React.cloneElement(ICONS.PAINT_BRUSH, { className: "w-4 h-4 mr-2" })} {t('projectPage.assetGenerator.generateLogoButton')}
        </button>
        <button onClick={onOpenDevSpecModal} className="btn-secondary w-full flex items-center justify-center p-2.5 text-sm" disabled={isAnyAssetGeneratorLoading}>
          {React.cloneElement(ICONS.CPU_CHIP, { className: "w-4 h-4 mr-2" })} {t('projectPage.assetGenerator.generateAIDevSpecButton')}
        </button>
        <button onClick={onOpenBlogPostModal} className="btn-secondary w-full flex items-center justify-center p-2.5 text-sm" disabled={isAnyAssetGeneratorLoading}>
          {React.cloneElement(ICONS.PENCIL_SQUARE, { className: "w-4 h-4 mr-2" })} {t('projectPage.assetGenerator.generateBlogPostButton')}
        </button>
        <button onClick={onOpenLandingPageModal} className="btn-secondary w-full flex items-center justify-center p-2.5 text-sm" disabled={isAnyAssetGeneratorLoading}>
          {React.cloneElement(ICONS.LINK, { className: "w-4 h-4 mr-2" })} {t('projectPage.assetGenerator.generateLandingPageButton')}
        </button>
      </div>
    </section>
  );
};

export default AIAssetGeneratorSection;
