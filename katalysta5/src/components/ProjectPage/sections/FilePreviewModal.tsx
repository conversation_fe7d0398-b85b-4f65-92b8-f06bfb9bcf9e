
import React from 'react';
import { useTranslation } from 'react-i18next';
import { ProjectFile } from '../../../types';
import { ICONS } from '../../../config';
import MarkdownRenderer from '../../MarkdownRenderer';

interface FilePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  file: ProjectFile | null;
}

const FilePreviewModal: React.FC<FilePreviewModalProps> = ({ isOpen, onClose, file }) => {
  const { t } = useTranslation();

  if (!isOpen || !file) return null;

  const renderContent = () => {
    if (!file.content) {
      return <p className="text-neutral-500">{t('projectPage.filePreview.noContent')}</p>;
    }

    if (file.type.startsWith('image/') && file.content.startsWith('data:image')) {
      return <img src={file.content} alt={file.name} className="max-w-full max-h-[60vh] object-contain mx-auto" />;
    }
    if (file.type === 'text/markdown') {
      return <div className="prose prose-sm max-w-none"><MarkdownRenderer markdown={file.content} /></div>;
    }
    if (file.type === 'text/html') {
      return <iframe srcDoc={file.content} title={file.name} className="w-full h-[60vh] border border-neutral-300" sandbox="allow-scripts allow-same-origin" />;
    }
    if (file.type === 'application/json' || file.type === 'text/plain') {
      return <pre className="whitespace-pre-wrap break-all bg-secondary p-3 rounded-md text-xs">{file.content}</pre>;
    }
    return <p className="text-neutral-500">{t('projectPage.filePreview.unsupportedType', { fileType: file.type })}</p>;
  };

  return (
    <div
      className="fixed inset-0 bg-neutral/60 backdrop-blur-sm flex items-center justify-center z-[1050] p-4"
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby="file-preview-modal-title"
    >
      <div
        className="bg-base-100 p-5 sm:p-6 rounded-xl shadow-2xl w-full max-w-2xl max-h-[85vh] flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center mb-4">
          <h3 id="file-preview-modal-title" className="text-lg font-semibold text-neutral-800 truncate pr-4" title={file.name}>
            {t('projectPage.filePreview.title')}: {file.name}
          </h3>
          <button onClick={onClose} className="p-1 text-neutral-500 hover:text-error rounded-full focus-visible:ring-2 focus-visible:ring-error" aria-label={t('common.close')}>
            {ICONS.X_MARK && React.cloneElement(ICONS.X_MARK, { className: "w-5 h-5" })}
          </button>
        </div>
        <div className="flex-grow overflow-y-auto custom-scrollbar pr-1">
          {renderContent()}
        </div>
        <div className="mt-6 text-right">
          <button onClick={onClose} className="btn-secondary px-4 py-2 text-sm">
            {t('common.close')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default FilePreviewModal;
