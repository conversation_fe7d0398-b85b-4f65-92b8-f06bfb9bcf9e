
import React from 'react';
import { useTranslation } from 'react-i18next';
import { ProjectFile } from '../../../types';
import { ICONS } from '../../../config';
import LoadingSpinner from '../../LoadingSpinner';
import MarkdownRenderer from '../../MarkdownRenderer';
import { ProjectPageSectionProps } from '../ProjectPage.types'; // sectionRef removed from this type

interface LandingPageDisplaySectionProps extends ProjectPageSectionProps { // No longer Omit
  landingPageDetails: { content: string | null; file: ProjectFile | null; specFile: ProjectFile | null };
  isLoading: boolean;
}

const LandingPageDisplaySection: React.FC<LandingPageDisplaySectionProps> = ({ project, landingPageDetails, isLoading }) => {
  const { t } = useTranslation();

  return (
    <section id="landing-page" className="bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl"> {/* Removed pt-20 -mt-20 */}
      <h3 className="text-xl font-semibold text-neutral mb-3 flex items-center">
        {React.cloneElement(ICONS.LINK, { className: "w-5 h-5 mr-2 text-ai-synergy" })}
        {t('projectPage.landingPage.title')}
      </h3>
      {isLoading ? (
        <LoadingSpinner message={t('projectPage.landingPage.generatingButton')} />
      ) : (
        <>
          {landingPageDetails.specFile?.content && (
            <div className="mb-4">
              <h4 className="text-md font-semibold text-neutral-700 mb-2">{t('projectPage.landingPage.specTitle')}</h4>
              <div className="prose prose-sm max-w-none p-3 bg-ai-synergy/5 border border-ai-synergy/20 rounded-md shadow-inner max-h-96 overflow-y-auto custom-scrollbar">
                <MarkdownRenderer markdown={landingPageDetails.specFile.content} />
              </div>
            </div>
          )}
          {landingPageDetails.file?.content && (
            <div className="mb-4">
              <h4 className="text-md font-semibold text-neutral-700 mb-2">{t('projectPage.landingPage.previewTitle', { projectName: project.name })}</h4>
              <div className="border border-neutral-300 rounded-md shadow-inner overflow-hidden">
                <iframe
                  srcDoc={landingPageDetails.content}
                  title={t('projectPage.landingPage.previewTitle', { projectName: project.name })}
                  className="w-full h-[500px]"
                  sandbox="allow-scripts"
                />
              </div>
            </div>
          )}
          {(!landingPageDetails.file && !landingPageDetails.specFile) && (
            <p className="text-sm text-neutral-500">{t('projectPage.assetGenerator.contentNotYetGenerated', { assetName: t('projectPage.landingPage.title') })}</p>
          )}
        </>
      )}
    </section>
  );
};

export default LandingPageDisplaySection;
