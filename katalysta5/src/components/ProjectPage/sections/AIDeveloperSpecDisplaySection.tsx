
import React from 'react';
import { useTranslation } from 'react-i18next';
import { ProjectFile } from '../../../types';
import { ICONS } from '../../../config';
import LoadingSpinner from '../../LoadingSpinner';
import MarkdownRenderer from '../../MarkdownRenderer';
import { ProjectPageSectionProps } from '../ProjectPage.types'; // sectionRef removed from this type

interface AIDeveloperSpecDisplaySectionProps extends ProjectPageSectionProps { // No longer Omit
  aiDeveloperSpecFile: ProjectFile | undefined;
  isLoading: boolean;
}

const AIDeveloperSpecDisplaySection: React.FC<AIDeveloperSpecDisplaySectionProps> = ({ aiDeveloperSpecFile, isLoading }) => {
  const { t } = useTranslation();

  return (
    <section id="ai-developer-specification" className="bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl"> {/* Removed pt-20 -mt-20 */}
      <h3 className="text-xl font-semibold text-neutral mb-3 flex items-center">
        {React.cloneElement(ICONS.CODE_BLOCK, { className: "w-5 h-5 mr-2 text-ai-technical" })}
        {t('projectPage.aiDeveloperSpecificationTitle')}
      </h3>
      {isLoading ? (
        <LoadingSpinner message={t('projectPage.generatingAIDevSpec')} />
      ) : aiDeveloperSpecFile?.content ? (
        <div className="prose prose-sm max-w-none p-3 bg-ai-technical/5 border border-ai-technical/20 rounded-md shadow-inner">
          <MarkdownRenderer markdown={aiDeveloperSpecFile.content} />
        </div>
      ) : (
        <p className="text-sm text-neutral-500">{t('projectPage.assetGenerator.contentNotYetGenerated', { assetName: t('projectPage.aiDeveloperSpecificationTitle') })}</p>
      )}
    </section>
  );
};

export default AIDeveloperSpecDisplaySection;
