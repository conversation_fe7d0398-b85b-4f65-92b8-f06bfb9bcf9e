

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Project, ProjectFile } from '../../../types';
import { ICONS } from '../../../config';
import LoadingSpinner from '../../LoadingSpinner';
import { ProjectPageSectionProps } from '../ProjectPage.types'; // sectionRef removed from this type
import FilePreviewModal from './FilePreviewModal';
import FileEditModal from './FileEditModal';
import { useProjects } from '../../../hooks/useProjects';
import { useNotifications } from '../../../contexts/NotificationContext';


interface ProjectFilesSectionProps extends ProjectPageSectionProps { // No longer Omit
  onDownloadAllFiles: () => void;
  isZipping: boolean;
}

const ProjectFilesSection: React.FC<ProjectFilesSectionProps> = ({ project, onDownloadAllFiles, isZipping }) => {
  const { t } = useTranslation();
  const { deleteProjectFile, duplicateProjectFile, updateProjectFileContent } = useProjects();
  const { showNotification } = useNotifications();

  const [fileToPreview, setFileToPreview] = useState<ProjectFile | null>(null);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [fileToEdit, setFileToEdit] = useState<ProjectFile | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const handlePreviewFile = (file: ProjectFile) => {
    setFileToPreview(file);
    setIsPreviewModalOpen(true);
  };

  const handleEditFile = (file: ProjectFile) => {
    const editableTypes = ['text/plain', 'text/markdown', 'application/json', 'text/html'];
    if (editableTypes.includes(file.type) && file.content !== undefined) {
        setFileToEdit(file);
        setIsEditModalOpen(true);
    } else {
        showNotification({type: 'info', title: t('notifications.editNotSupportedTitle'), message: t('notifications.editNotSupportedMessage', {fileType: file.type})});
    }
  };

  const handleCopyContent = async (file: ProjectFile) => {
    if (!file.content) {
      showNotification({ type: 'info', title: t('notifications.copyNotPossibleTitle'), message: t('notifications.copyNoContent')});
      return;
    }
    try {
      await navigator.clipboard.writeText(file.content);
      showNotification({ type: 'success', title: t('notifications.copySuccessTitle'), message: t('notifications.copySuccessMessage', {fileName: file.name})});
    } catch (err) {
      console.error('Failed to copy file content:', err);
      showNotification({ type: 'error', title: t('notifications.copyErrorTitle'), message: t('common.copyFailed') });
    }
  };

  const handleDuplicateFile = (fileId: string) => {
    const fileToActOn = project.files.find(f=>f.id===fileId);
    if (window.confirm(t('notifications.confirmDuplicateFile', {fileName: fileToActOn?.name || 'this file'}))) {
        duplicateProjectFile(project.id, fileId);
    }
  };
  
  const handleDeleteFile = (fileId: string) => {
    const fileToActOn = project.files.find(f=>f.id===fileId);
    if (window.confirm(t('notifications.confirmDeleteFile', {fileName: fileToActOn?.name || 'this file'}))) {
      deleteProjectFile(project.id, fileId);
    }
  };

  const handleSaveEditedFile = (fileId: string, newContent: string) => {
    updateProjectFileContent(project.id, fileId, newContent);
  };

  const canCopyOrEdit = (file: ProjectFile) => {
    const supportedTypes = ['text/plain', 'text/markdown', 'application/json', 'text/html'];
    return supportedTypes.includes(file.type) && file.content !== undefined;
  }

  return (
    <>
      <section id="project-files" className="bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl"> {/* Removed pt-20 -mt-20 */}
        <div className="flex justify-between items-center mb-3">
          <h2 className="text-xl font-semibold text-neutral flex items-center">
            {ICONS.FOLDER && React.cloneElement(ICONS.FOLDER, { className: "w-5 h-5 mr-2 text-neutral-500" })}
            {t('projectPage.filesTitle')}
          </h2>
          <button
            onClick={onDownloadAllFiles}
            disabled={isZipping || !project.files.some(f => f.content || (project.strategicOutput?.generatedImage?.url && f.name === "project_main_image.jpg"))}
            className="btn-secondary text-xs px-3 py-1.5 flex items-center disabled:opacity-50"
          >
            {isZipping ? <LoadingSpinner size="xs" /> : ICONS.DOWNLOAD && React.cloneElement(ICONS.DOWNLOAD, { className: "w-3.5 h-3.5" })}
            <span className="ml-1.5">{t('projectPage.downloadAllAsZip')}</span>
          </button>
        </div>
        {project.files.length > 0 ? (
          <ul className="space-y-2">
            {project.files.map(file => (
              <li key={file.id} className="text-sm text-neutral-700 flex flex-col sm:flex-row justify-between sm:items-center p-2.5 bg-secondary/50 rounded hover:bg-secondary transition-colors group shadow-sm">
                <button 
                    onClick={() => handlePreviewFile(file)} 
                    className="text-left hover:text-primary flex-grow mb-2 sm:mb-0 mr-2"
                    title={t('projectPage.fileActions.previewTooltip', {fileName: file.name})}
                >
                  <span className="font-medium">{file.name}</span> ({file.type}, {Math.round(file.size / 1024)} KB)
                </button>
                <div className="flex items-center space-x-1.5 self-end sm:self-center">
                   {canCopyOrEdit(file) && (
                    <button onClick={() => handleCopyContent(file)} className="p-1.5 text-neutral-500 hover:text-primary rounded hover:bg-primary/10 transition-colors" title={t('projectPage.fileActions.copyTooltip', {fileName: file.name})}>
                      {ICONS.CLIPBOARD && React.cloneElement(ICONS.CLIPBOARD, { className: "w-4 h-4"})}
                    </button>
                  )}
                  {canCopyOrEdit(file) && (
                    <button onClick={() => handleEditFile(file)} className="p-1.5 text-neutral-500 hover:text-accent rounded hover:bg-accent/10 transition-colors" title={t('projectPage.fileActions.editTooltip', {fileName: file.name})}>
                      {ICONS.PENCIL_SQUARE && React.cloneElement(ICONS.PENCIL_SQUARE, { className: "w-4 h-4"})}
                    </button>
                  )}
                  <button onClick={() => handleDuplicateFile(file.id)} className="p-1.5 text-neutral-500 hover:text-info rounded hover:bg-info/10 transition-colors" title={t('projectPage.fileActions.duplicateTooltip', {fileName: file.name})}>
                    {ICONS.PLUS_CIRCLE && React.cloneElement(ICONS.PLUS_CIRCLE, { className: "w-4 h-4"})}
                  </button>
                  <button onClick={() => handleDeleteFile(file.id)} className="p-1.5 text-neutral-500 hover:text-error rounded hover:bg-error/10 transition-colors" title={t('projectPage.fileActions.deleteTooltip', {fileName: file.name})}>
                    {ICONS.X_MARK && React.cloneElement(ICONS.X_MARK, { className: "w-4 h-4"})}
                  </button>
                   {file.content && (
                    <a
                        href={file.type === 'text/html' ? URL.createObjectURL(new Blob([file.content], {type: 'text/html'})) : `data:${file.type};charset=utf-8,${encodeURIComponent(file.content)}`}
                        download={file.name}
                        className="p-1.5 text-neutral-500 hover:text-primary rounded hover:bg-primary/10 transition-colors"
                        title={t('projectPage.downloadFileTooltip', {fileName: file.name})}
                        onClick={(e) => e.stopPropagation()} 
                    >
                        {ICONS.DOWNLOAD && React.cloneElement(ICONS.DOWNLOAD, {className: "w-4 h-4"})}
                    </a>
                  )}
                </div>
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-neutral-500 text-sm">{t('projectPage.noFiles')}</p>
        )}
      </section>

      <FilePreviewModal
        isOpen={isPreviewModalOpen}
        onClose={() => setIsPreviewModalOpen(false)}
        file={fileToPreview}
      />
      <FileEditModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        file={fileToEdit}
        onSave={handleSaveEditedFile}
      />
    </>
  );
};

export default ProjectFilesSection;
