
import React from 'react';
import { useTranslation } from 'react-i18next';
import { ProjectFile } from '../../../types';
import { ICONS } from '../../../config';
import MarkdownRenderer from '../../MarkdownRenderer';
import { ProjectPageSectionProps } from '../ProjectPage.types'; // sectionRef removed from this type

interface ProjectBlueprintSectionProps extends ProjectPageSectionProps { // No longer Omit
  markdownBlueprintFile: ProjectFile | undefined;
}

const ProjectBlueprintSection: React.FC<ProjectBlueprintSectionProps> = ({ markdownBlueprintFile }) => {
  const { t } = useTranslation();

  if (!markdownBlueprintFile?.content) return null;

  return (
    <section id="project-blueprint" className="bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl"> {/* Removed pt-20 -mt-20 */}
      <h3 className="text-xl font-semibold text-neutral mb-3 flex items-center">
        {ICONS.DOCUMENT_TEXT && React.cloneElement(ICONS.DOCUMENT_TEXT, { className: "w-5 h-5 mr-2 text-ai-developer-prompt" })}
        {t('projectPage.nav.blueprint')}
      </h3>
      <div className="prose prose-sm max-w-none p-3 bg-ai-developer-prompt/5 border border-ai-developer-prompt/20 rounded-md shadow-inner">
        <MarkdownRenderer markdown={markdownBlueprintFile.content} />
      </div>
    </section>
  );
};

export default ProjectBlueprintSection;
