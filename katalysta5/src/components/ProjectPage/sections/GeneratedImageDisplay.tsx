
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Project, GeneratedImageDetails } from '../../../types';
import { ICONS } from '../../../config';
import LoadingSpinner from '../../LoadingSpinner';

interface GeneratedImageDisplayProps {
  project: Project;
  imageType: 'main' | 'logo' | 'icon' | 'banner';
  imageDetails: GeneratedImageDetails | undefined;
  titleKey: string;
  altKey: string;
  isLoadingThisImage: boolean;
  isLoadingAlternativesThisImage: boolean;
  showPrompt: boolean;
  onTogglePrompt: () => void;
  onGenerate: (imageType: 'main' | 'logo' | 'icon' | 'banner', existingPrompt?: string) => void;
  onGenerateAlternatives: (imageType: 'main' | 'logo' | 'icon' | 'banner') => void;
  onSelectAlternative: (imageType: 'main' | 'logo' | 'icon' | 'banner', alternativeUrl: string) => void;
  onOpenLogoModal?: () => void; // Specific for logo if modal is preferred for initial generation
}

// Note: The parent div that might have had "pt-20 -mt-20" is in ProjectPage.tsx itself
// This component just renders its content directly.
const GeneratedImageDisplay: React.FC<GeneratedImageDisplayProps> = ({
  project,
  imageType,
  imageDetails,
  titleKey,
  altKey,
  isLoadingThisImage,
  isLoadingAlternativesThisImage,
  showPrompt,
  onTogglePrompt,
  onGenerate,
  onGenerateAlternatives,
  onSelectAlternative,
  onOpenLogoModal
}) => {
  const { t } = useTranslation();

  return (
    // If this component was a direct child section with pt-20, it would be removed from here.
    // However, it seems to be a sub-component used within a larger "Generated Images" section.
    // The section that contains these displays ("generated-image-section" in ProjectPage) will handle its overall padding.
    <div id={`generated-${imageType}-image-display`}> {/* Added an id for potential direct styling if needed */}
      <h3 className="text-lg font-semibold text-neutral-800 mb-2 flex items-center">
        {ICONS.IMAGE && React.cloneElement(ICONS.IMAGE, { className: "w-4 h-4 mr-2" })}
        {t(titleKey)}
      </h3>
      {isLoadingThisImage ? (
        <div className="min-h-[200px] flex items-center justify-center">
            <LoadingSpinner message={t('projectPage.imageGeneration.generating', { asset: t(titleKey) })} />
        </div>
      ) : imageDetails?.url ? (
        <div className="p-3 bg-neutral-50 border border-neutral-200/70 rounded-md shadow-sm">
          <img 
            src={imageDetails.url} 
            alt={t(altKey, { projectName: project.name })} 
            className="w-full h-auto max-h-72 object-contain rounded-md mb-2.5 shadow" 
          />
          <div className="text-xs text-neutral-500 mb-2.5">
            <button onClick={onTogglePrompt} className="hover:underline text-primary">
              {showPrompt ? t('projectPage.imageGeneration.hidePrompt') : t('projectPage.imageGeneration.showPrompt')}
            </button>
            {showPrompt && <pre className="mt-1 p-1.5 bg-neutral-100 rounded-md whitespace-pre-wrap text-xs">{imageDetails.prompt}</pre>}
          </div>
          <div className="flex flex-wrap gap-1.5">
            <button onClick={() => onGenerate(imageType, imageDetails.prompt)} disabled={isLoadingThisImage || isLoadingAlternativesThisImage} className="btn-secondary text-xs px-2.5 py-1">
              {isLoadingThisImage ? <LoadingSpinner size="xs"/> : ICONS.REFRESH && React.cloneElement(ICONS.REFRESH, {className:"w-3 h-3"})}
              <span className="ml-1">{t('projectPage.imageGeneration.regenerateButton')}</span>
            </button>
            <button onClick={() => onGenerateAlternatives(imageType)} disabled={isLoadingThisImage || isLoadingAlternativesThisImage} className="btn-secondary text-xs px-2.5 py-1">
              {isLoadingAlternativesThisImage ? <LoadingSpinner size="xs"/> : ICONS.PALETTE && React.cloneElement(ICONS.PALETTE, {className:"w-3 h-3"})}
              <span className="ml-1">{t('projectPage.imageGeneration.alternativesButton')}</span>
            </button>
          </div>
          {imageDetails.alternatives && imageDetails.alternatives.length > 0 && (
            <div className="mt-3">
              <h4 className="text-xs font-semibold text-neutral-600 mb-1.5">{t('projectPage.imageGeneration.alternativesTitle')}</h4>
              <div className="grid grid-cols-3 sm:grid-cols-4 gap-1.5">
                {imageDetails.alternatives.map((altUrl, index) => (
                  <button 
                    key={index} 
                    onClick={() => onSelectAlternative(imageType, altUrl)} 
                    className="border-2 border-transparent hover:border-primary focus:border-primary rounded-md overflow-hidden focus:outline-none focus-visible:ring-1 focus-visible:ring-primary"
                    title={`${t('projectPage.imageGeneration.alternativeText')} ${index + 1}`}
                  >
                    <img src={altUrl} alt={`${t('projectPage.imageGeneration.alternativeText')} ${index + 1}`} className="w-full h-16 sm:h-20 object-cover" />
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="p-3 bg-neutral-100 border-2 border-dashed border-neutral-200/80 rounded-md text-center min-h-[150px] flex flex-col items-center justify-center">
          <p className="text-xs text-neutral-500 mb-2.5">{t('projectPage.imageGeneration.noImageYet', { asset: t(titleKey) })}</p>
          <button 
            onClick={() => imageType === 'logo' && onOpenLogoModal ? onOpenLogoModal() : onGenerate(imageType)} 
            disabled={isLoadingThisImage} 
            className="btn-primary text-xs px-2.5 py-1"
          >
            {ICONS.WAND && React.cloneElement(ICONS.WAND, {className:"w-3 h-3"})}
            <span className="ml-1">{t('projectPage.imageGeneration.generateButton')}</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default GeneratedImageDisplay;
