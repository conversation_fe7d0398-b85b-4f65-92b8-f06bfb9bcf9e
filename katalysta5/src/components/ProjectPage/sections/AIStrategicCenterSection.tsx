
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Project } from '../../../types';
import { ICONS } from '../../../config';
import LoadingSpinner from '../../LoadingSpinner';
import { ProjectPageSectionProps } from '../ProjectPage.types'; // sectionRef removed from this type

interface AIStrategicCenterSectionProps extends ProjectPageSectionProps { // No longer Omit
  onRunDeepAnalysis: () => void;
  isAnalyzing: boolean;
  isProjectsHookLoadingGlobal: boolean;
}

const AIStrategicCenterSection: React.FC<AIStrategicCenterSectionProps> = ({
  project,
  onRunDeepAnalysis,
  isAnalyzing,
  isProjectsHookLoadingGlobal
}) => {
  const { t } = useTranslation();

  return (
    <section id="ai-strategic-center" className="bg-gradient-to-br from-primary/5 to-accent/5 p-5 sm:p-6 rounded-lg shadow-xl border border-primary/20"> {/* Removed pt-20 -mt-20 */}
      <h2 className="text-2xl font-semibold text-neutral mb-4 flex items-center">
        {ICONS.AI_SPARKLE} <span className="ml-2">{t('projectPage.aiStrategicCenter')}</span>
      </h2>
      <p className="text-sm text-neutral-600 mb-4">
        {t('projectPage.aiStrategicCenterDescription')}
      </p>
      <button
        onClick={onRunDeepAnalysis}
        disabled={isAnalyzing || isProjectsHookLoadingGlobal}
        className="bg-accent hover:bg-accent-dark text-base-100 font-semibold inline-flex items-center px-5 py-2.5 text-sm rounded-md shadow-md disabled:opacity-60 transition-colors"
      >
        {isAnalyzing || project.status.startsWith('analyzing_deep_') || project.status === 'analyzing_initial' ? (
          <>
            <LoadingSpinner size="xs" color="text-base-100" containerClassName="flex items-center" />
            <span className="ml-2">{project.detailedStatusMessageKey ? t(project.detailedStatusMessageKey) : t('projectPage.aiAnalyzing')}</span>
          </>
        ) : (
          <>
            {ICONS.LIGHTBULB} <span className="ml-2">{t('projectPage.runDeepAnalysis')}</span>
          </>
        )}
      </button>
      {project.status === 'error' && project.errorDetails && <p className="text-error mt-2 text-sm">{t('projectPage.errorDuringAnalysis', { errorDetails: project.errorDetails })}</p>}
    </section>
  );
};

export default AIStrategicCenterSection;
