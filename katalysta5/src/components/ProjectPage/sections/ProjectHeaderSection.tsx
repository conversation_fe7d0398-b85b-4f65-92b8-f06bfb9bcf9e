import React from 'react';
import { useTranslation } from 'react-i18next';
import { Project } from '../../../types';
import { ICONS } from '../../../config';
// ProjectPageSectionProps a jej Omit boli odstránené, keďže sectionRef sa už nepoužíva globálne

interface ProjectHeaderSectionProps { // Upravené z ProjectPageSectionProps
  project: Project;
  onSaveChanges: () => void;
}

const ProjectHeaderSection: React.FC<ProjectHeaderSectionProps> = ({ project, onSaveChanges }) => {
  const { t, i18n } = useTranslation();

  const projectName = project?.name ?? t('common.error');
  const projectDescription = project?.description ?? t('common.error');
  const createdAtDate = project?.createdAt ? new Date(project.createdAt).toLocaleDateString(i18n.language) : 'N/A';
  const lastUpdatedDate = project?.lastInteractionTimestamp || project?.updatedAt 
                          ? new Date(project.lastInteractionTimestamp || project.updatedAt!).toLocaleString(i18n.language) 
                          : 'N/A';
  
  const statusKey = project?.status 
    ? `projectCard.status${project.status.charAt(0).toUpperCase() + project.status.slice(1).replace(/_([a-z])/g, (g) => g[1].toUpperCase())}` 
    : 'projectCard.statusError';
  const statusText = t(statusKey as any, project?.status || 'error');

  return (
    <section id="project-header" className="bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl">
      <div className="flex flex-col md:flex-row justify-between md:items-start mb-4 pb-4 border-b border-neutral-200/70">
        <div className="flex-1 min-w-0">
          <h1 className="text-2xl sm:text-3xl font-bold text-primary mb-1 truncate" title={projectName}>{projectName}</h1>
          <p className="text-sm text-neutral-600 line-clamp-3" title={projectDescription}>{projectDescription}</p>
          <p className="text-xs text-neutral-500 mt-1.5">
            {t('projectPage.created')}: {createdAtDate} | {t('projectPage.lastUpdated')}: {lastUpdatedDate}
          </p>
        </div>
        <div className="mt-4 md:mt-0 md:ml-4 flex-shrink-0">
          <button
            onClick={onSaveChanges}
            className="btn-primary inline-flex items-center px-3 py-1.5 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium rounded-md shadow-sm"
          >
            {ICONS.SAVE} <span className="ml-2">{t('projectPage.saveChanges')}</span>
          </button>
        </div>
      </div>
      <div className="text-xs font-medium">
        Status: <span className={`font-semibold px-2 py-1 rounded-full ${project?.status === 'error' ? 'bg-status-error/20 text-status-error' : project?.status?.includes('analyzing') ? 'bg-status-pending/20 text-status-pending animate-pulse' : 'bg-status-success/20 text-status-success'}`}>
          {project?.detailedStatusMessageKey ? t(project.detailedStatusMessageKey) : statusText}
        </span>
        {project?.status === 'error' && project.errorDetails && <p className="text-error/80 mt-1">{project.errorDetails}</p>}
      </div>
    </section>
  );
};

export default ProjectHeaderSection;