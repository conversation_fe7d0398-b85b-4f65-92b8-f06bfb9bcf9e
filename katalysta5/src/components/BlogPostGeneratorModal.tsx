
import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Project, BlogPostFormData } from '../types';
import { useProjects } from '../hooks/useProjects';
import { useNotifications } from '../contexts/NotificationContext';
import { ICONS } from '../config'; // Updated import
import LoadingSpinner from './LoadingSpinner';

interface BlogPostGeneratorModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: Project | null;
  onSubmit: (formData: BlogPostFormData) => Promise<void>;
  isLoading: boolean; // Loading state controlled by the parent (ProjectPage)
}

const blogPostFormFields: Array<{ name: keyof BlogPostFormData; labelKey: string; type: 'text' | 'textarea' | 'select'; options?: Array<{value: string, labelKey: string}>; placeholderKey?: string; required?: boolean }> = [
  { name: 'topic', labelKey: 'projectPage.assetGenerator.modal.blogPost.topicLabel', type: 'text', placeholderKey: 'projectPage.assetGenerator.modal.blogPost.topicPlaceholder', required: true },
  { 
    name: 'postGoal', 
    labelKey: 'projectPage.assetGenerator.modal.blogPost.postGoalLabel', 
    type: 'select',
    options: [
      { value: '', labelKey: 'common.selectOption' },
      { value: 'educate', labelKey: 'projectPage.assetGenerator.modal.blogPost.goalOptions.educate' },
      { value: 'promote', labelKey: 'projectPage.assetGenerator.modal.blogPost.goalOptions.promote' },
      { value: 'inform', labelKey: 'projectPage.assetGenerator.modal.blogPost.goalOptions.inform' },
      { value: 'other_goal', labelKey: 'common.other' }
    ],
    required: true
  },
  { 
    name: 'postLength', 
    labelKey: 'projectPage.assetGenerator.modal.blogPost.postLengthLabel', 
    type: 'select',
    options: [
      { value: '', labelKey: 'common.selectOption' },
      { value: 'short', labelKey: 'projectPage.assetGenerator.modal.blogPost.lengthOptions.short' },
      { value: 'medium', labelKey: 'projectPage.assetGenerator.modal.blogPost.lengthOptions.medium' },
      { value: 'long', labelKey: 'projectPage.assetGenerator.modal.blogPost.lengthOptions.long' },
    ]
  },
  { name: 'targetAudienceBlog', labelKey: 'projectPage.assetGenerator.modal.blogPost.targetAudienceBlogLabel', type: 'textarea', placeholderKey: 'projectPage.assetGenerator.modal.blogPost.targetAudienceBlogPlaceholder' },
  { 
    name: 'tone', 
    labelKey: 'projectPage.assetGenerator.modal.blogPost.toneLabel', 
    type: 'select',
    options: [
      { value: '', labelKey: 'common.selectOption' },
      { value: 'formal', labelKey: 'projectPage.assetGenerator.modal.blogPost.toneOptions.formal' },
      { value: 'friendly', labelKey: 'projectPage.assetGenerator.modal.blogPost.toneOptions.friendly' },
      { value: 'technical', labelKey: 'projectPage.assetGenerator.modal.blogPost.toneOptions.technical' },
      { value: 'motivational', labelKey: 'projectPage.assetGenerator.modal.blogPost.toneOptions.motivational' },
      { value: 'other_tone', labelKey: 'common.other' }
    ]
  },
  { name: 'keyMessages', labelKey: 'projectPage.assetGenerator.modal.blogPost.keyMessagesLabel', type: 'textarea', placeholderKey: 'projectPage.assetGenerator.modal.blogPost.keyMessagesPlaceholder' },
  { name: 'preferredKeywords', labelKey: 'projectPage.assetGenerator.modal.blogPost.preferredKeywordsLabel', type: 'text', placeholderKey: 'projectPage.assetGenerator.modal.blogPost.preferredKeywordsPlaceholder' },
  { name: 'postStructure', labelKey: 'projectPage.assetGenerator.modal.blogPost.postStructureLabel', type: 'textarea', placeholderKey: 'projectPage.assetGenerator.modal.blogPost.postStructurePlaceholder' },
  { name: 'ctaText', labelKey: 'projectPage.assetGenerator.modal.blogPost.ctaTextLabel', type: 'text', placeholderKey: 'projectPage.assetGenerator.modal.blogPost.ctaTextLabel' },
  { name: 'ctaLink', labelKey: 'projectPage.assetGenerator.modal.blogPost.ctaLinkLabel', type: 'text', placeholderKey: 'projectPage.assetGenerator.modal.blogPost.ctaLinkPlaceholder' },
];

export const BlogPostGeneratorModal: React.FC<BlogPostGeneratorModalProps> = ({ isOpen, onClose, project, onSubmit, isLoading }) => {
  const { t } = useTranslation();
  const { getAIAssistanceForBlogPostForm, updateProjectDataCore } = useProjects();
  const { showNotification } = useNotifications();

  const initialFormData: BlogPostFormData = {
    topic: project?.name || '',
    postGoal: '',
    postLength: 'medium',
    targetAudienceBlog: '',
    tone: 'friendly',
    keyMessages: '',
    preferredKeywords: project?.initialAnalysis?.keywords?.join(', ') || '',
    postStructure: '',
    ctaText: '',
    ctaLink: '',
  };
  
  const [formData, setFormData] = useState<BlogPostFormData>(initialFormData);
  const [isFillingWithAI, setIsFillingWithAI] = useState(false);

  useEffect(() => {
    if (project && isOpen) {
      setFormData(prev => ({
        ...initialFormData, 
        topic: project.name || initialFormData.topic,
        preferredKeywords: project.initialAnalysis?.keywords?.join(', ') || initialFormData.preferredKeywords,
        ...(project.dataCore.blogPostFormData || {}) 
      }));
    }
  }, [project, isOpen]); 

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleFillWithAI = async () => {
    if (!project) return;
    setIsFillingWithAI(true);
    try {
      const aiAssistedData = await getAIAssistanceForBlogPostForm(project);
      const updatedFormData = { ...formData, ...aiAssistedData };
      setFormData(updatedFormData);
      showNotification({ type: 'success', title: t('notifications.aiAssistedFormFillTitle'), message: t('notifications.aiAssistedFormFillSuccess') });
    } catch (error: any) {
      showNotification({ type: 'error', title: t('notifications.aiAssistedFormFillErrorTitle'), message: error.message });
    } finally {
      setIsFillingWithAI(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!project) return;

    const requiredTextualFields: (keyof BlogPostFormData)[] = ['topic', 'postGoal'];
    for (const fieldName of requiredTextualFields) {
        const fieldConfig = blogPostFormFields.find(f => f.name === fieldName);
        if (!formData[fieldName] || String(formData[fieldName]).trim() === '') {
            showNotification({ 
                type: 'warning', 
                title: t('notifications.inputRequiredTitle'), 
                message: t('notifications.missingRequiredField', { fieldName: fieldConfig ? t(fieldConfig.labelKey) : fieldName }) 
            });
            return;
        }
    }

    updateProjectDataCore(project.id, { blogPostFormData: formData });
    await onSubmit(formData);
  };
  
  const commonInputClasses = "block w-full px-3 py-1.5 border border-neutral-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm text-xs bg-base-100";

  if (!isOpen) return null;
   if (!project) {
    return (
      <div className="fixed inset-0 bg-neutral-900/70 backdrop-blur-sm flex items-center justify-center z-[1000] p-4" onClick={onClose}>
        <div className="bg-base-100 p-6 rounded-xl shadow-2xl w-full max-w-md text-center" onClick={e => e.stopPropagation()}>
          <p>{t('projectPage.projectNotFound')}</p>
          <button onClick={onClose} className="btn-secondary mt-4">{t('common.close')}</button>
        </div>
      </div>
    );
  }

  return (
    <div 
        className="fixed inset-0 bg-neutral-900/70 backdrop-blur-sm flex items-center justify-center z-[1000] p-4 transition-opacity duration-300 ease-in-out"
        onClick={onClose}
        role="dialog"
        aria-modal="true"
        aria-labelledby="blog-post-modal-title"
    >
      <div 
        className="bg-base-100 rounded-xl shadow-2xl w-full max-w-3xl max-h-[90vh] flex flex-col"
        onClick={e => e.stopPropagation()}
      >
        <div className="flex justify-between items-center p-4 sm:p-5 border-b border-neutral-200">
          <h3 id="blog-post-modal-title" className="text-lg font-semibold text-neutral-800 flex items-center">
            {ICONS.PENCIL_SQUARE && React.cloneElement(ICONS.PENCIL_SQUARE, {className:"w-5 h-5 mr-2 text-primary"})}
            {t('projectPage.assetGenerator.modal.blogPost.title')}
          </h3>
          <button onClick={onClose} className="p-1 text-neutral-500 hover:text-error rounded-full focus-visible:ring-2 focus-visible:ring-error" aria-label={t('common.close')}>
            {ICONS.X_MARK && React.cloneElement(ICONS.X_MARK, {className: "w-5 h-5"})}
          </button>
        </div>

        <form onSubmit={handleSubmit} className="flex-grow contents">
          <div className="p-4 sm:p-5 flex-grow overflow-y-auto custom-scrollbar">
            <div className="flex justify-between items-center mb-3">
              <p className="text-xs text-neutral-600">{t('projectPage.assetGenerator.modal.blogPost.formDescription')}</p>
              <button
                type="button"
                onClick={handleFillWithAI}
                disabled={isFillingWithAI || isLoading}
                className="btn-secondary text-xs px-2 py-1 flex items-center disabled:opacity-50"
              >
                {isFillingWithAI ? <LoadingSpinner size="xs"/> : ICONS.AI_SPARKLE && React.cloneElement(ICONS.AI_SPARKLE, {className:"w-3 h-3"})}
                <span className="ml-1">{t('projectPage.assetGenerator.modal.fillWithAIButton')}</span>
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3">
              {blogPostFormFields.map(field => (
                <div key={field.name} className={field.type === 'textarea' && ['targetAudienceBlog', 'keyMessages', 'postStructure'].includes(field.name) ? 'md:col-span-2' : ''}>
                  <label htmlFor={`bp_${field.name}`} className="block text-xs font-medium text-neutral-700">
                    {t(field.labelKey)} {field.required && <span className="text-error">*</span>}
                  </label>
                  {field.type === 'select' && field.options ? (
                    <select id={`bp_${field.name}`} name={field.name} value={String(formData[field.name] || '')} onChange={handleInputChange} className={commonInputClasses} required={field.required}>
                      {field.options.map(opt => <option key={opt.value} value={opt.value}>{t(opt.labelKey)}</option>)}
                    </select>
                  ) : field.type === 'textarea' ? (
                    <textarea id={`bp_${field.name}`} name={field.name} value={String(formData[field.name] || '')} onChange={handleInputChange} rows={2} className={commonInputClasses} placeholder={field.placeholderKey ? t(field.placeholderKey) : undefined} required={field.required}/>
                  ) : (
                    <input type="text" id={`bp_${field.name}`} name={field.name} value={String(formData[field.name] || '')} onChange={handleInputChange} className={commonInputClasses} placeholder={field.placeholderKey ? t(field.placeholderKey) : undefined} required={field.required}/>
                  )}
                </div>
              ))}
            </div>
          </div>

          <div className="p-4 sm:p-5 border-t border-neutral-200 flex justify-end space-x-3">
            <button type="button" onClick={onClose} className="btn-secondary px-4 py-2 text-sm">
              {t('projectPage.assetGenerator.modal.cancelButton')}
            </button>
            <button
              type="submit"
              disabled={isLoading || isFillingWithAI}
              className="btn-primary flex items-center justify-center px-4 py-2 text-sm"
            >
              {isLoading ? (
                <LoadingSpinner variant="dots" size="xs" color="text-base-100" icon={React.cloneElement(ICONS.AI_SPARKLE, {className:"w-3.5 h-3.5"})} containerClassName="flex flex-row items-center"/>
              ) : (
                ICONS.AI_SPARKLE && React.cloneElement(ICONS.AI_SPARKLE, {className:"w-4 h-4"})
              )}
              <span className="ml-2">{t('projectPage.assetGenerator.modal.generateButton')}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};