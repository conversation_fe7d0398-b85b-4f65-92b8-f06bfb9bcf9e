
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Project, AISuggestion, ProjectTemplate } from '../types';
import { ICONS } from '../config'; // Updated import
import KnowledgeGraphVisualizer from './KnowledgeGraphVisualizer';
import AISuggestionCard from './AISuggestionCard';

interface QuickViewModalProps {
  project: Project | null;
  onClose: () => void;
  onGoToProject: (projectId: string) => void;
}

const QuickViewModal: React.FC<QuickViewModalProps> = ({ project, onClose, onGoToProject }) => {
  const { t, i18n } = useTranslation();

  if (!project) return null;
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(i18n.language);
  };

  return (
    <div 
        className="fixed inset-0 bg-neutral/50 backdrop-blur-sm flex items-center justify-center z-[900] p-4 transition-opacity duration-300 ease-in-out"
        onClick={onClose}
        role="dialog"
        aria-modal="true"
        aria-labelledby="quick-view-title"
    >
      <div 
        className="bg-base-100 p-5 sm:p-6 rounded-xl shadow-2xl w-full max-w-xl max-h-[85vh] overflow-y-auto relative transform transition-all duration-300 ease-in-out scale-95 group-hover:scale-100"
        onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside modal
      >
        <button
          onClick={onClose}
          className="absolute top-3 right-3 p-1.5 text-neutral/60 hover:text-error rounded-full hover:bg-neutral/10 transition-colors"
          aria-label={t('common.close')}
        >
          {ICONS.X_MARK && React.cloneElement(ICONS.X_MARK, { className: "w-5 h-5"})}
        </button>

        <h2 id="quick-view-title" className="text-2xl font-bold text-primary mb-2 flex items-center">
          {ICONS.PROJECT} <span className="ml-2">{project.name}</span>
        </h2>
        <p className="text-sm text-neutral/70 mb-3">{project.description}</p>
        <p className="text-xs text-neutral/50 mb-4">
          {t('projectPage.created')}: {formatDate(project.createdAt)} | {t('projectPage.lastUpdated')}: {formatDate(project.updatedAt)}
        </p>

        {project.initialAnalysis && (
          <div className="mb-4 p-3 bg-secondary/50 rounded-md">
            <h3 className="text-sm font-semibold text-neutral mb-1">{t('quickViewModal.initialAiAnalysis')}</h3>
            <p className="text-xs text-neutral/80"><strong>{t('projectCard.aiTypeGuess')}</strong> {project.initialAnalysis.projectTypeGuess}</p>
            {project.initialAnalysis.summary && <p className="text-xs text-neutral/80 italic"><strong>{t('projectCard.aiSummary')}</strong> "{project.initialAnalysis.summary}"</p>}
            {project.initialAnalysis.keywords.length > 0 && (
              <div className="mt-1">
                <strong className="text-xs text-neutral/80">{t('projectCard.aiKeywords')}</strong>
                <div className="flex flex-wrap gap-1 mt-0.5">
                  {project.initialAnalysis.keywords.map(keyword => (
                    <span key={keyword} className="px-1.5 py-0.5 bg-accent/20 text-accent-dark text-xs rounded-full">
                      {keyword}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
        
        {project.strategicOutput && (
            <div className="mb-4 p-3 bg-primary/5 rounded-md">
                <h3 className="text-sm font-semibold text-neutral mb-1">{t('quickViewModal.strategicInsightsSummary')}</h3>
                {project.strategicOutput.projectPageNarrative && 
                    <p className="text-xs text-neutral/80 mb-1 line-clamp-3"><strong>{t('projectPage.projectNarrativeTitle')}:</strong> {project.strategicOutput.projectPageNarrative}</p>}
                {project.strategicOutput.strategicSuggestions && project.strategicOutput.strategicSuggestions.length > 0 &&
                    <p className="text-xs text-neutral/80 mb-1"><strong>{t('projectPage.strategicConsultantTitle')}:</strong> {project.strategicOutput.strategicSuggestions[0].title} ({project.strategicOutput.strategicSuggestions.length} {t('quickViewModal.totalSuggestions')})</p>}
                {project.strategicOutput.knowledgeGraph && project.strategicOutput.knowledgeGraph.summary &&
                     <p className="text-xs text-neutral/80 line-clamp-2"><strong>{t('knowledgeGraph.aiSummary')}</strong> {project.strategicOutput.knowledgeGraph.summary}</p>}
            </div>
        )}


        <div className="mt-6 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-neutral/80 bg-secondary hover:bg-neutral/10 rounded-md border border-neutral/30"
          >
            {t('common.close')}
          </button>
          <button
            onClick={() => onGoToProject(project.id)}
            className="btn-primary inline-flex items-center px-4 py-2 text-sm font-medium rounded-md shadow-sm"
          >
            {t('quickViewModal.goToFullProject')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default QuickViewModal;
