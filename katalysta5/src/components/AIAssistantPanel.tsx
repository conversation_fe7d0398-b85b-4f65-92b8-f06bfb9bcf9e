
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNotifications } from '../contexts/NotificationContext'; // Added
import { Project, AIAssistantPrompt, AIAssistantPromptCategory, AssistantMessage, AIAssistantNote } from '../types';
import { useProjects } from '../hooks/useProjects';
import LoadingSpinner from './LoadingSpinner';
import { ICONS } from '../config'; 
import { v4 as uuidv4 } from 'uuid';

interface AIAssistantPanelProps {
  project: Project | null;
}

const AIAssistantPanel: React.FC<AIAssistantPanelProps> = ({ project }) => {
  const { t, i18n } = useTranslation();
  const { showNotification } = useNotifications(); // Added
  const { executeProjectAssistantPrompt, addAssistantNoteToDataCore, isLoading: isProjectsHookLoading, addConceptualFileToProject } = useProjects();

  const [activeTab, setActiveTab] = useState<'basic' | 'super' | 'notes'>('basic');
  const [selectedPrompt, setSelectedPrompt] = useState<AIAssistantPrompt | null>(null);
  const [userInput, setUserInput] = useState<string>('');
  const [contextInput, setContextInput] = useState<string>('');
  const [aiResponse, setAiResponse] = useState<string | null>(null);
  const [isLoadingResponse, setIsLoadingResponse] = useState<boolean>(false);
  const [noteTitle, setNoteTitle] = useState<string>('');
  const [showSaveNoteFields, setShowSaveNoteFields] = useState<boolean>(false);

  const responseContainerRef = useRef<HTMLDivElement>(null);

  const basicPrompts: AIAssistantPromptCategory[] = [
    {
      categoryTitle: t('aiAssistant.basicPrompts.titleSection'),
      prompts: [
        { id: 'basic_summarize', title: t('aiAssistant.basicPrompts.summarize.title'), description: t('aiAssistant.basicPrompts.summarize.description'), promptTemplate: "Summarize the following text concisely (1-2 paragraphs):\n\n{{userInput}}", category: 'Basic', requiresUserInput: true, requiresContextInput: false },
        { id: 'basic_keywords', title: t('aiAssistant.basicPrompts.extractKeywords.title'), description: t('aiAssistant.basicPrompts.extractKeywords.description'), promptTemplate: "Extract the 5-7 most important keywords from this text:\n\n{{userInput}}", category: 'Basic', requiresUserInput: true, requiresContextInput: false },
        { id: 'basic_explain', title: t('aiAssistant.basicPrompts.explainConcept.title'), description: t('aiAssistant.basicPrompts.explainConcept.description'), promptTemplate: "Explain the concept of '{{userInput}}' in simple terms, suitable for a beginner.", category: 'Basic', requiresUserInput: true, requiresContextInput: false },
      ]
    }
  ];
  
  useEffect(() => {
    if (aiResponse && responseContainerRef.current) {
        responseContainerRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' });
    }
  }, [aiResponse]);

  const handleTabChange = (tab: 'basic' | 'super' | 'notes') => {
    setActiveTab(tab);
    setSelectedPrompt(null);
    setAiResponse(null);
    setUserInput('');
    setContextInput('');
    setShowSaveNoteFields(false);
  };

  const handlePromptSelect = (prompt: AIAssistantPrompt) => {
    setSelectedPrompt(prompt);
    setAiResponse(null);
    setUserInput(''); 
    setContextInput(''); 
    setShowSaveNoteFields(false);
    setNoteTitle(t('aiAssistant.saveNoteTitlePlaceholder', { promptTitle: prompt.title }));
  };

  const handleExecutePrompt = async () => {
    if (!project || !selectedPrompt) return;
    if (selectedPrompt.requiresUserInput && !userInput.trim()) {
      showNotification({type: 'warning', title: t('notifications.inputRequiredTitle'), message: t('aiAssistant.errorUserInputRequired')});
      return;
    }

    setIsLoadingResponse(true);
    setAiResponse(null);
    setShowSaveNoteFields(false);

    const result = await executeProjectAssistantPrompt(project.id, selectedPrompt.promptTemplate, userInput, contextInput);
    setAiResponse(result);
    setIsLoadingResponse(false);
  };

  const handleSaveNote = () => {
    if (!project || !aiResponse || !selectedPrompt) return;
    const finalNoteTitle = noteTitle.trim() || t('aiAssistant.saveNoteTitlePlaceholder', { promptTitle: selectedPrompt.title });
    addAssistantNoteToDataCore(project.id, finalNoteTitle, aiResponse, selectedPrompt.title, contextInput);
    showNotification({type: 'success', title: t('notifications.noteSavedSuccessTitle'), message: t('aiAssistant.noteSaved')});
    setShowSaveNoteFields(false);
  };

  const handleCreateMarkdownFileFromNote = (note: AIAssistantNote) => {
    if (!project) return;

    const sanitizedTitle = note.title.replace(/[^\w\s-]/gi, '').replace(/\s+/g, '_');
    const fileName = `AI_Assistant_Note_-_${sanitizedTitle || 'Untitled'}.md`;

    const markdownContent = `
# ${note.title}

_Generated on: ${new Date(note.createdAt).toLocaleString(i18n.language)}_

## Prompt Used
\`\`\`
${note.promptUsed}
\`\`\`

${note.contextProvided ? `## Context Provided
\`\`\`text
${note.contextProvided}
\`\`\`
` : '## Context Provided\n_No additional context was provided._'}

---

## AI Output
${note.content}
    `;

    addConceptualFileToProject(project.id, fileName, 'text/markdown', markdownContent.trim().length, markdownContent.trim());
    showNotification({type: 'success', title: t('notifications.fileCreatedSuccessTitle'), message: t('aiAssistant.notes.markdownFileCreated', { fileName })});
  };

  const renderPromptList = (promptCategories: AIAssistantPromptCategory[]) => {
    return promptCategories.map((category, catIndex) => (
        <div key={catIndex} className="mb-4">
            <h4 className="text-sm font-semibold text-neutral-600 mb-1.5">{category.categoryTitle}</h4>
            <ul className="space-y-1.5">
            {category.prompts.map(prompt => (
                <li key={prompt.id}>
                <button
                    onClick={() => handlePromptSelect(prompt)}
                    className={`w-full text-left px-3 py-2 text-xs rounded-md transition-colors 
                                ${selectedPrompt?.id === prompt.id 
                                    ? 'bg-primary text-base-100 shadow-sm' 
                                    : 'bg-secondary hover:bg-primary/10 hover:text-primary'
                                }`}
                >
                    {prompt.title}
                    <p className={`text-xs mt-0.5 ${selectedPrompt?.id === prompt.id ? 'text-primary/70' : 'text-neutral-500'}`}>{prompt.description}</p>
                </button>
                </li>
            ))}
            </ul>
        </div>
    ));
  };
  
  const renderSavedNotes = () => {
    if (!project || !project.dataCore.assistantNotes || project.dataCore.assistantNotes.length === 0) {
        return <p className="text-neutral-600 text-sm">{t('aiAssistant.notes.noNotes')}</p>;
    }
    return (
        <div className="space-y-3">
            {project.dataCore.assistantNotes.slice().reverse().map(note => ( 
                <details key={note.id} className="bg-secondary/50 p-3 rounded-md shadow-sm group">
                    <summary className="text-sm font-semibold text-primary cursor-pointer group-open:mb-2 flex justify-between items-center">
                        <div>
                            {note.title} 
                            <span className="text-xs text-neutral-500 ml-2">({new Date(note.createdAt).toLocaleDateString(i18n.language)})</span>
                        </div>
                    </summary>
                    <div className="text-xs space-y-1.5">
                        {note.promptUsed && <p><strong>{t('aiAssistant.notes.promptUsedLabel')}</strong> {note.promptUsed}</p>}
                        {note.contextProvided && <p><strong>{t('aiAssistant.notes.contextProvidedLabel')}</strong> <span className="line-clamp-2 italic">{note.contextProvided}</span></p>}
                        <p><strong>{t('aiAssistant.notes.aiOutputLabel')}</strong></p>
                        <pre className="whitespace-pre-wrap bg-base-100 p-2 rounded text-neutral-700 text-xs border border-neutral-200">{note.content}</pre>
                        <button
                            onClick={() => handleCreateMarkdownFileFromNote(note)}
                            className="mt-2 text-xs text-accent hover:text-accent/80 flex items-center px-2 py-1 bg-accent/10 rounded-md border border-accent/20 hover:border-accent/30"
                            title={t('aiAssistant.notes.createMarkdownFileTooltip')}
                        >
                            {ICONS.DOCUMENT_TEXT && React.cloneElement(ICONS.DOCUMENT_TEXT, { className: "w-3.5 h-3.5 mr-1"})}
                            {t('aiAssistant.notes.createMarkdownFile')}
                        </button>
                    </div>
                </details>
            ))}
        </div>
    );
  };


  if (!project) return <LoadingSpinner message={t('projectPage.loadingProject')} />;

  const isSuperAssistantReady = project.status === 'analyzed_deep' && project.strategicOutput?.suggestedAssistantPrompts && project.strategicOutput.suggestedAssistantPrompts.length > 0;

  return (
    <div className="bg-base-100 p-4 sm:p-6 rounded-lg shadow-xl mt-8">
      <h3 className="text-xl font-semibold text-neutral mb-1 flex items-center">
        {ICONS.WAND && React.cloneElement(ICONS.WAND, {className:"w-5 h-5 mr-2 text-accent"})}
        <span>{t('aiAssistant.title')}</span>
      </h3>
      <p className="text-xs text-neutral-500 mb-4">
        {activeTab === 'super' && !isSuperAssistantReady ? t('aiAssistant.superAssistantNotReadyDetailed') : 
         activeTab === 'super' && isSuperAssistantReady ? t('aiAssistant.superAssistantDescription') :
         t('aiAssistant.basicAssistantDescription')
        }
      </p>

      <div className="flex border-b border-neutral-200 mb-4">
        {(['basic', 'super', 'notes'] as const).map(tab => (
          <button
            key={tab}
            onClick={() => handleTabChange(tab)}
            className={`px-3 py-2 text-sm font-medium -mb-px border-b-2 transition-colors
                        ${activeTab === tab 
                            ? 'border-primary text-primary' 
                            : 'border-transparent text-neutral-500 hover:text-primary hover:border-primary/30'
                        }`}
            aria-current={activeTab === tab ? 'page' : undefined}
          >
            {t(`aiAssistant.tabs.${tab}`)}
          </button>
        ))}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Prompts List / Notes List */}
        <div className="md:col-span-1 space-y-4 max-h-[60vh] md:max-h-[calc(100vh-20rem)] overflow-y-auto pr-2 custom-scrollbar">
          {activeTab === 'basic' && renderPromptList(basicPrompts)}
          {activeTab === 'super' && isSuperAssistantReady && project.strategicOutput?.suggestedAssistantPrompts && renderPromptList(project.strategicOutput.suggestedAssistantPrompts)}
          {activeTab === 'super' && !isSuperAssistantReady && <p className="text-neutral-600 text-sm p-4 bg-secondary/50 rounded-md">{t('aiAssistant.superAssistantNotReadyDetailed')}</p>}
          {activeTab === 'notes' && renderSavedNotes()}
        </div>

        {/* Interaction Area */}
        <div className="md:col-span-2 space-y-4">
          {!selectedPrompt && activeTab !== 'notes' && (
            <div className="p-6 bg-secondary/30 rounded-md text-center text-neutral-600 flex flex-col items-center justify-center min-h-[200px]">
              {React.cloneElement(ICONS.LIGHTBULB, { className: "w-10 h-10 mb-3 text-neutral-400" })}
              <p>{t('aiAssistant.selectPromptToStart')}</p>
            </div>
          )}

          {selectedPrompt && (activeTab === 'basic' || activeTab === 'super') && (
            <>
              <div>
                <h4 className="text-md font-semibold text-primary mb-1">{selectedPrompt.title}</h4>
                <p className="text-xs text-neutral-500 mb-3">{selectedPrompt.description}</p>
              </div>

              {selectedPrompt.requiresUserInput && (
                <div>
                  <label htmlFor="userInput" className="block text-sm font-medium text-neutral-700 mb-1">{t('aiAssistant.userInputLabel')}</label>
                  <textarea
                    id="userInput"
                    value={userInput}
                    onChange={(e) => setUserInput(e.target.value)}
                    rows={3}
                    className="w-full p-2 border border-neutral-300 rounded-md shadow-sm text-sm focus:ring-primary focus:border-primary"
                    placeholder={t('aiAssistant.userInputPlaceholder')}
                  />
                </div>
              )}
              
              {selectedPrompt.requiresContextInput && (
                <div>
                  <label htmlFor="contextInput" className="block text-sm font-medium text-neutral-700 mb-1">{t('aiAssistant.contextInputLabel')}</label>
                  <textarea
                    id="contextInput"
                    value={contextInput}
                    onChange={(e) => setContextInput(e.target.value)}
                    rows={5}
                    className="w-full p-2 border border-neutral-300 rounded-md shadow-sm text-sm focus:ring-primary focus:border-primary"
                    placeholder={t('aiAssistant.contextInputPlaceholder')}
                  />
                </div>
              )}

              <button
                onClick={handleExecutePrompt}
                disabled={isLoadingResponse || isProjectsHookLoading}
                className="btn-accent inline-flex items-center px-4 py-2 text-sm rounded-md shadow-sm disabled:opacity-60"
              >
                {isLoadingResponse || isProjectsHookLoading ? (
                  <>
                    <LoadingSpinner 
                      variant="dots" 
                      size="xs" 
                      color="text-base-100" 
                      icon={React.cloneElement(ICONS.AI_SPARKLE, { className: "w-3 h-3"})} 
                      iconClassName="w-3 h-3" 
                      containerClassName="flex flex-row items-center justify-center"
                      messageClassName="ml-1.5" // Smaller margin for button context
                    />
                  </>
                ) : (
                  <>
                    {React.cloneElement(ICONS.AI_SPARKLE, {className:"w-4 h-4"})}
                    <span className="ml-2">{t('aiAssistant.executePrompt')}</span>
                  </>
                )}
              </button>
              
              <div ref={responseContainerRef}>
                {isLoadingResponse && !aiResponse && <LoadingSpinner variant="dots" icon={React.cloneElement(ICONS.AI_SPARKLE, {className:"w-5 h-5 text-accent"})} message={t('aiAssistant.processing')} containerClassName="flex flex-row items-center justify-center space-x-2 p-4" />}
                {aiResponse && (
                  <div className="mt-4 p-3 border border-neutral-200 rounded-md bg-secondary/30">
                    <h5 className="text-sm font-semibold text-neutral-800 mb-1.5">{t('aiAssistant.aiResponseTitle')}</h5>
                    <pre className="whitespace-pre-wrap break-words text-sm text-neutral-700 leading-relaxed">{aiResponse}</pre>
                    
                    {!showSaveNoteFields && (
                        <button 
                            onClick={() => setShowSaveNoteFields(true)}
                            className="mt-3 text-xs text-primary hover:underline flex items-center"
                        >
                           {ICONS.SAVE && React.cloneElement(ICONS.SAVE, { className: "w-3.5 h-3.5 mr-1"})} {t('aiAssistant.saveThisResponse')}
                        </button>
                    )}

                    {showSaveNoteFields && (
                         <div className="mt-3 pt-3 border-t border-neutral-300 space-y-2">
                            <div>
                                <label htmlFor="noteTitle" className="block text-xs font-medium text-neutral-600 mb-0.5">{t('aiAssistant.saveNoteTitleLabel')}</label>
                                <input 
                                    type="text"
                                    id="noteTitle"
                                    value={noteTitle}
                                    onChange={(e) => setNoteTitle(e.target.value)}
                                    placeholder={t('aiAssistant.saveNoteTitlePlaceholder', {promptTitle: selectedPrompt.title})}
                                    className="w-full p-1.5 border border-neutral-300 rounded-md text-xs focus:ring-primary focus:border-primary"
                                />
                            </div>
                            <button
                                onClick={handleSaveNote}
                                className="btn-secondary px-3 py-1 text-xs rounded-md shadow-sm"
                            >
                                {ICONS.SAVE && React.cloneElement(ICONS.SAVE, { className: "w-3.5 h-3.5 mr-1"})} {t('aiAssistant.saveResponseAsNote')}
                            </button>
                         </div>
                    )}
                  </div>
                )}
                {aiResponse && aiResponse.startsWith(t('serviceMessages.apiKeyMissingError')) && (
                     <p className="text-status-error text-xs mt-1">{aiResponse}</p>
                )}
                 {aiResponse && aiResponse.startsWith(t('serviceMessages.assistantExecutionError', {error:''}).split(':')[0]) && (
                     <p className="text-status-error text-xs mt-1">{aiResponse}</p>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default AIAssistantPanel;
