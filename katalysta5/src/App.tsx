
import React from 'react';
import { Routes, Route, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import Dashboard from './components/Dashboard';
import ProjectPage from './components/ProjectPage'; // Updated import
import Header from './components/Header';
import HowAIWorksPage from './components/HowAIWorksPage';
import NewAppIdeaPage from './components/NewAppIdeaPage';
import ProAppPage from './components/ProAppPage';
import DynamicAppPage from './components/DynamicAppPage';
import NotificationToastContainer from './components/NotificationToastContainer'; 

const App: React.FC = () => {
  const { t } = useTranslation();
  const currentYear = new Date().getFullYear();

  return (
    <div className="min-h-screen flex flex-col bg-secondary">
      <Header />
      <main className="flex-grow container mx-auto p-4 sm:p-6 lg:p-8 relative"> {/* Added relative for positioning context if needed */}
        <NotificationToastContainer /> {/* Added */}
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/project/:projectId" element={<ProjectPage />} />
          <Route path="/how-ai-works" element={<HowAIWorksPage />} />
          <Route path="/new-app-idea" element={<NewAppIdeaPage />} />
          <Route path="/pro-appka" element={<ProAppPage />} />
          <Route path="/dynamic-app" element={<DynamicAppPage />} /> 
        </Routes>
      </main>
      <footer className="py-6 text-center text-neutral-70 text-sm">
        <p>&copy; {t('footerCopyright', { year: currentYear })}</p>
        <p className="mt-1">
          <Link to="/how-ai-works" className="hover:text-primary underline">
            {t('footerHowAiWorksLink')}
          </Link>
        </p>
      </footer>
    </div>
  );
};

export default App;