
import { GoogleGenAI, GenerateContentResponse, GenerateImagesResponse } from "@google/genai";
import {
    AIInitialAnalysis,
    AIStrategicOutput,
    KnowledgeGraph,
    AISuggestion,
    ProjectFile,
    Project,
    ProjectTemplate,
    PortfolioAnalysis,
    AIAssistantPromptCategory,
    GeneratedImageDetails,
    LogoBriefData,
    AIDevSpecFormData,
    BlogPostFormData,
    LandingPageFormData
} from '../types';
import {
    MODELS,
    OPTIMIZED_APP_IDEA_META_PROMPT,
    META_PROMPT_FOR_UNIVERSAL_APP_CREATION,
    META_PROMPT_FOR_DYNAMIC_APP_PROMPT_GENERATION,
    DOCUMENTATION_GENERATION_META_PROMPT,
    BLOG_POST_GENERATION_META_PROMPT,
    LANDING_PAGE_HTML_GENERATION_META_PROMPT,
    AI_AGENT_SPECIFICATION_GENERATION_META_PROMPT,
    GENERIC_ASSET_GENERATION_META_PROMPT,
    AI_DEVELOPER_SPEC_OUTPUT_SPECS,
    BLOG_POST_OUTPUT_SPECS,
    LANDING_PAGE_CONTENT_AND_VISUAL_SPEC_OUTPUT_SPECS
} from '../config'; // Updated import
import i18n from '../i18n';

const API_KEY = process.env.API_KEY;

if (!API_KEY) {
  console.error("API_KEY for Gemini is not set. Please set process.env.API_KEY.");
}

const ai = API_KEY ? new GoogleGenAI({ apiKey: API_KEY }) : null;

const mapLangToName = (langCode: string): string => {
  if (langCode.startsWith('sk')) return 'Slovak';
  if (langCode.startsWith('en')) return 'English';
  return 'English'; // Default to English
};

const mapLangToHtmlTag = (langCode: string): string => {
  return langCode.split('-')[0]; // e.g., "en-US" -> "en", "sk" -> "sk"
}

const parseJsonSafe = <T,>(jsonString: string, context: string): T | null => {
  try {
    let cleanJsonString = jsonString.trim();
    const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
    const match = cleanJsonString.match(fenceRegex);
    if (match && match[2]) {
      cleanJsonString = match[2].trim();
    }

    try {
      return JSON.parse(cleanJsonString) as T;
    } catch (initialParseError) {
      let originalErrorForLog = initialParseError; 

      // Attempt "儂" fix for "Assistant Prompts"
      if (context === "Assistant Prompts" && cleanJsonString.includes("儂")) {
        const repairRegex = /\]\s*儂\s*\{/gm;
        if (repairRegex.test(cleanJsonString)) {
          console.warn(`Attempting to repair JSON for ${context} due to '儂' character.`);
          const fixedJsonString = cleanJsonString.replace(repairRegex, "},{");
          try {
            const parsedRepairedJson = JSON.parse(fixedJsonString) as T;
            console.info(`Successfully parsed repaired JSON for ${context} after '儂' fix.`);
            return parsedRepairedJson;
          } catch (repairedParseError) {
            console.error(`Error parsing repaired JSON for ${context} after '儂' fix:`, repairedParseError, "Repaired string attempt:", fixedJsonString);
            originalErrorForLog = repairedParseError;
          }
        }
      }

      // Attempt "Knowledge Graph" type line fix
      if (context === "Knowledge Graph" && initialParseError instanceof SyntaxError) {
        const malformedTypeLineRegex = /^(\s*"type":\s*"(?:entity|concept)")\s+(?![,\}\]])\S.*$/gm;
        const potentiallyFixedString = cleanJsonString.replace(malformedTypeLineRegex, '$1');
        
        if (potentiallyFixedString !== cleanJsonString) {
          console.warn(`Attempting to repair JSON for ${context} due to malformed 'type' line. Original snippet (first 500 chars): ${cleanJsonString.substring(0,500)}`);
          try {
            const parsedRepairedJson = JSON.parse(potentiallyFixedString) as T;
            console.info(`Successfully parsed repaired JSON for ${context} after 'type' line fix.`);
            return parsedRepairedJson;
          } catch (repairedParseError) {
            console.error(`Error parsing repaired JSON for ${context} after 'type' line fix:`, repairedParseError, "Repaired string attempt (first 500 chars):", potentiallyFixedString.substring(0,500));
            originalErrorForLog = repairedParseError; 
          }
        }
      }

      // Attempt "Strategic Suggestions" junk between objects fix
      if (context === "Strategic Suggestions" && initialParseError instanceof SyntaxError) {
        // Regex to find: (valid_end_of_property_value)(junk_and_bad_comma)(start_of_next_id_field)
        // Example: "type": "opportunity"   dirigente ",    "id": 
        // Group 1: ("type":\s*"(?:opportunity|risk|next_step|synergy)"|"priority":\s*"(?:high|medium|low)")
        // Group 2 (junk): (\s*[^,{}\s][^,}]*?\s*,\s*)
        // Group 3 (next id): ("\s*id"\s*:)
        const junkBetweenObjectsRegex = /("type":\s*"(?:opportunity|risk|next_step|synergy)"|"priority":\s*"(?:high|medium|low)")(\s*[^,{}\s][^,}]*?\s*,\s*)("\s*id"\s*:)/gm;
        if (junkBetweenObjectsRegex.test(cleanJsonString)) {
            console.warn(`Attempting to repair JSON for ${context} due to junk between objects. Original snippet (first 500 chars): ${cleanJsonString.substring(0,500)}`);
            const potentiallyFixedString = cleanJsonString.replace(junkBetweenObjectsRegex, '$1}, $3');
            try {
                const parsedRepairedJson = JSON.parse(potentiallyFixedString) as T;
                console.info(`Successfully parsed repaired JSON for ${context} after "junk between objects" fix.`);
                return parsedRepairedJson;
            } catch (repairedParseError) {
                console.error(`Error parsing repaired JSON for ${context} after "junk between objects" fix:`, repairedParseError, "Repaired string attempt (first 500 chars):", potentiallyFixedString.substring(0,500));
                originalErrorForLog = repairedParseError;
            }
        }
      }
      
      throw originalErrorForLog;
    }
  } catch (error) { 
    console.error(`Error parsing JSON for ${context}:`, error, "Original string (first 1000 chars):", jsonString.substring(0, 1000) + (jsonString.length > 1000 ? "..." : ""));
    return null;
  }
};


const handleApiError = (error: unknown, lng: string, defaultErrorKey: string): { title: string, summary: string, isQuotaError: boolean } => {
    let errorMessage = error instanceof Error ? error.message : String(error);
    let titleKey = defaultErrorKey;
    let summaryKey = `${defaultErrorKey}Summary`; 
    let isQuotaError = false;

    const quotaErrorSubstrings = ["resource has been exhausted", "quota", "429"];
    if (quotaErrorSubstrings.some(sub => errorMessage.toLowerCase().includes(sub))) {
        titleKey = 'serviceMessages.apiQuotaExceededErrorTitle';
        summaryKey = 'serviceMessages.apiQuotaExceededErrorDetail';
        isQuotaError = true;
    } else if (!API_KEY) {
        titleKey = 'serviceMessages.apiKeyMissingError';
        summaryKey = 'serviceMessages.apiKeyMissingErrorSummary';
    } else {
        titleKey = defaultErrorKey;
        summaryKey = `${defaultErrorKey}Summary`;
    }

    const finalTitle = i18n.exists(titleKey, {lng}) ? i18n.t(titleKey, { lng }) : i18n.t('serviceMessages.unknownError', {lng});
    const finalSummary = i18n.exists(summaryKey, {lng}) ? i18n.t(summaryKey, { error: errorMessage, lng }) : errorMessage;

    return {
        title: finalTitle,
        summary: finalSummary,
        isQuotaError
    };
};

const withRetry = async <T>(fn: () => Promise<T>, retries = 2, delayMs = 1000): Promise<T> => {
  try {
    return await fn();
  } catch (error: any) {
    const errorMessage = String(error.message || error).toLowerCase();
    const isNetworkOrRpcError = errorMessage.includes("xhr error") || errorMessage.includes("network error") || errorMessage.includes("rpc failed");
    
    if (retries > 0 && isNetworkOrRpcError) {
      console.warn(`Retrying API call... ${retries} attempts left. Error:`, error.message);
      await new Promise(resolve => setTimeout(resolve, delayMs));
      return withRetry(fn, retries - 1, delayMs * 2); 
    }
    throw error; 
  }
};

const buildProjectContextForAIFormFill = (project: Project, currentLang: string): string => {
    let context = `Project Name: ${project.name}\n`;
    context += `Project Description: ${project.description}\n`;
    if (project.initialAnalysis) {
        context += `Initial AI Analysis - Type: ${project.initialAnalysis.projectTypeGuess || 'N/A'}, Keywords: ${project.initialAnalysis.keywords?.join(', ') || 'N/A'}, Summary: ${project.initialAnalysis.summary || 'N/A'}\n`;
    }
    if (project.strategicOutput?.projectPageNarrative) {
        context += `Project Narrative (Deep Analysis): ${project.strategicOutput.projectPageNarrative.substring(0, 500)}...\n`;
    }
    const blueprintFile = project.files.find(f => f.type === 'text/markdown' && f.content && (f.name.includes('Blueprint') || f.name.includes('Developer_Prompt')));
    if (blueprintFile?.content) {
        context += `Project Blueprint Content (first 1000 chars):\n${blueprintFile.content.substring(0, 1000)}...\n`;
    } else {
        const textFiles = project.files.filter(f => f.type === 'text/plain' || f.type === 'text/markdown' || f.type === 'application/json');
        if (textFiles.length > 0) {
            context += `\nKey File Contents (summarized first 200 chars each):\n`;
            textFiles.slice(0, 3).forEach(file => { 
                if (file.content) {
                    context += `--- ${file.name} ---\n${file.content.substring(0, 200)}...\n`;
                }
            });
        }
    }
    context += `\nTarget Language for Suggestions: ${mapLangToName(currentLang)}\n`;
    return context;
};


export const geminiService = {
  getInitialAnalysis: async (projectName: string, projectDescription: string, files: ProjectFile[], currentLang: string): Promise<AIInitialAnalysis> => {
    const lng = currentLang;
    if (!ai) return {
        projectTypeGuess: i18n.t('serviceMessages.apiKeyMissingError', {lng}),
        keywords: [],
        summary: i18n.t('serviceMessages.apiKeyMissingErrorSummary', {lng})
    };

    const languageName = mapLangToName(lng);
    const fileSummary = files.map(f => `${f.name} (${f.type}, ${Math.round(f.size/1024)}KB)`).join(', ');

    const prompt = `
      Analyze the following project details to provide an initial assessment.
      Project Name: "${projectName}"
      Project Description: "${projectDescription}"
      Associated Files: ${fileSummary || 'No files specified.'}

      Based on this information, please:
      1. Suggest a primary "Project Type" (e.g., Software Development, Research Paper, Marketing Campaign, Creative Product Design, Data Analysis Report).
      2. Extract 5-7 relevant "Keywords" or key themes.
      3. Provide a very brief one-sentence "Summary" of the project's apparent core goal.

      IMPORTANT: Generate all textual content (projectTypeGuess, keywords, summary) in ${languageName}.

      Return your response *ONLY* as a valid JSON object with the following structure.
      Strictly adhere to the JSON format. Do not include any comments, explanations, or conversational text within the JSON structure itself or outside of it. The entire response must be *only* the specified JSON object.
      {
        "projectTypeGuess": "Suggested Project Type in ${languageName}",
        "keywords": ["Keyword1 in ${languageName}", "Keyword2 in ${languageName}", ...],
        "summary": "One-sentence project summary in ${languageName}."
      }
    `;

    try {
      const response: GenerateContentResponse = await ai.models.generateContent({
        model: MODELS.TEXT,
        contents: [{ role: "user", parts: [{text: prompt}] }],
        config: { responseMimeType: "application/json" }
      });
      const resultJson = parseJsonSafe<AIInitialAnalysis>(response.text, "Initial Analysis");
      if (resultJson) {
        return resultJson;
      }
      throw new Error(i18n.t('serviceMessages.parseError', {lng}));
    } catch (error) {
      console.error("Error fetching initial analysis:", error);
      const { title, summary } = handleApiError(error, lng, 'serviceMessages.initialAnalysisError');
      return {
        projectTypeGuess: title,
        keywords: [],
        summary: summary,
      };
    }
  },

  generateProjectImage: async (project: Project, currentLang: string): Promise<GeneratedImageDetails | undefined> => {
    const lng = currentLang;
    if (!ai) {
        const errorMsg = i18n.t('serviceMessages.apiKeyMissingErrorSummary', {lng});
        console.warn(errorMsg);
        throw new Error(errorMsg);
    }

    const narrativeSummary = project.strategicOutput?.projectPageNarrative?.substring(0, 200) || project.description;
    const keywordsString = project.initialAnalysis?.keywords?.join(', ') || 'general project';

    const imagePrompt = `
      Create a visually inspiring and symbolic image representing a project.
      Project Essence: ${narrativeSummary}
      Key Themes: ${keywordsString}
      Project Type: ${project.initialAnalysis?.projectTypeGuess || 'diverse'}
      Desired Style: Abstract, conceptual, vibrant, optimistic, with a sense of innovation and depth. Avoid text. Focus on a modern, clean aesthetic.
    `;

    try {
      const response: GenerateImagesResponse = await withRetry(() => ai.models.generateImages({
        model: MODELS.IMAGE,
        prompt: imagePrompt,
        config: { numberOfImages: 1, outputMimeType: 'image/jpeg' },
      }));
      if (response.generatedImages && response.generatedImages.length > 0) {
        const base64ImageBytes = response.generatedImages[0].image.imageBytes;
        return { url: `data:image/jpeg;base64,${base64ImageBytes}`, prompt: imagePrompt, alternatives: [] };
      }
      return undefined;
    } catch (error) {
      console.error("Error generating project image:", error);
      const { summary } = handleApiError(error, lng, 'serviceMessages.imageGenerationError');
      throw new Error(summary);
    }
  },

  generateLogo: async (project: Project, currentLang: string): Promise<GeneratedImageDetails | undefined> => {
    const lng = currentLang;
    if (!ai) {
        const errorMsg = i18n.t('serviceMessages.apiKeyMissingErrorSummary', {lng});
        console.warn(errorMsg);
        throw new Error(errorMsg);
    }
    const keywordsString = project.initialAnalysis?.keywords?.join(', ') || project.name;
    const imagePrompt = `
        Create a clean, modern, and iconic logo for a project named '${project.name}'.
        Project core idea: ${project.description.substring(0,150)}
        Key themes/keywords: ${keywordsString}
        The logo should be symbolic, memorable, and suitable for digital branding (e.g., website favicon, app icon).
        It should be vector-like in quality, even if raster output.
        Avoid complex details; favor simplicity and strong visual impact.
        No text in the logo itself unless it's highly stylized and integral to a symbolic design (e.g. an initial).
        Output format: JPEG, but design as if it could be a vector.
    `;
     try {
      const response: GenerateImagesResponse = await withRetry(() => ai.models.generateImages({
        model: MODELS.IMAGE,
        prompt: imagePrompt,
        config: { numberOfImages: 1, outputMimeType: 'image/jpeg' },
      }));
      if (response.generatedImages && response.generatedImages.length > 0) {
        const base64ImageBytes = response.generatedImages[0].image.imageBytes;
        return { url: `data:image/jpeg;base64,${base64ImageBytes}`, prompt: imagePrompt, alternatives: [] };
      }
      return undefined;
    } catch (error) {
      console.error("Error generating logo:", error);
      const { summary } = handleApiError(error, lng, 'serviceMessages.imageGenerationError');
      throw new Error(summary);
    }
  },

  generateImageAlternatives: async (basePrompt: string, currentLang: string, numberOfAlternatives: number = 3): Promise<string[]> => {
    const lng = currentLang;
    if (!ai) {
      const errorMsg = i18n.t('serviceMessages.apiKeyMissingErrorSummary', {lng});
      console.warn(errorMsg);
      throw new Error(errorMsg);
    }
    try {
      const response: GenerateImagesResponse = await withRetry(() => ai.models.generateImages({
        model: MODELS.IMAGE,
        prompt: `${basePrompt} - Provide ${numberOfAlternatives} variations of this concept. Each should be distinct yet related.`,
        config: { numberOfImages: numberOfAlternatives, outputMimeType: 'image/jpeg' },
      }));
      if (response.generatedImages && response.generatedImages.length > 0) {
        return response.generatedImages.map(img => `data:image/jpeg;base64,${img.image.imageBytes}`);
      }
      return [];
    } catch (error) {
      console.error("Error generating image alternatives:", error);
      const { summary } = handleApiError(error, lng, 'serviceMessages.imageGenerationError');
      throw new Error(summary);
    }
  },

  performDeepAnalysis: async (project: Project, currentLang: string): Promise<Omit<AIStrategicOutput, 'generatedImage' | 'generatedLogo' | 'generatedIcon' | 'generatedBanner'>> => {
    const lng = currentLang;
    if (!ai) {
      const errorSummary = i18n.t('serviceMessages.apiKeyMissingErrorSummary', {lng});
      const defaultKnowledgeGraph: KnowledgeGraph = { elements: [], relations: [], summary: errorSummary };
      return {
        projectPageNarrative: i18n.t('serviceMessages.deepAnalysisDefaultNarrative', {lng}),
        suggestedTemplate: ProjectTemplate.STANDARD,
        knowledgeGraph: defaultKnowledgeGraph,
        strategicSuggestions: [{
            id: 'err1',
            title: i18n.t('serviceMessages.deepAnalysisDefaultSuggestionTitle', {lng}),
            description: errorSummary,
            type: 'risk'
        }],
        suggestedAssistantPrompts: []
      };
    }

    const languageName = mapLangToName(lng);
    const filesSummary = project.files.map(f => f.name).join(', ');
    const basePromptInfo = `
      Project Name: "${project.name}"
      Project Description: "${project.description}"
      Initial AI Analysis:
        Type: ${project.initialAnalysis?.projectTypeGuess || 'N/A'}
        Keywords: ${project.initialAnalysis?.keywords?.join(', ') || 'N/A'}
        Summary: ${project.initialAnalysis?.summary || 'N/A'}
      Files: ${filesSummary || 'None specified'}
      Current Language for Output: ${languageName}
    `;

    const narrativePrompt = `
      ${basePromptInfo}
      Generate a compelling and "living" project page narrative in ${languageName}. This narrative should:
      - Clearly articulate the project's mission and vision.
      - Highlight key objectives and anticipated outcomes/values.
      - Create an engaging story around the project that inspires and informs.
      - Be structured for readability on a webpage (e.g., use paragraphs).
      - Max 300 words.
      Return as a plain text string, ensuring the content is in ${languageName}.
    `;

    const kgPrompt = `
      ${basePromptInfo}
      Your task is to analyze the provided project information and generate a conceptual knowledge graph. This graph should identify key entities, concepts, and their relationships.

      **CRITICAL OUTPUT REQUIREMENTS: READ VERY CAREFULLY**
      1.  Your *entire* response text MUST BE a single, valid JSON object.
      2.  This JSON object MUST strictly conform to the "JSON Structure Definition" provided below.
      3.  There must be ABSOLUTELY NO other text, comments, explanations, dialogue, markdown fences (like \`\`\`json), or any non-JSON characters before the opening '{' or after the closing '}' of the JSON object.
      4.  The output must be directly parsable by a standard JSON.parse() function without any pre-processing other than a simple \`trim()\`.
      5.  Ensure all string values within the JSON are properly quoted and escaped if necessary.
      6.  Pay close attention to commas between elements in arrays and properties in objects. Do not insert any extraneous text or characters between valid JSON tokens.

      **Language Requirements:**
      -   The "label" fields for elements and relations should be in a common, understandable language (preferably English, or the primary language of the project data if discernible and simple).
      -   The "summary" field for the knowledge graph MUST be in ${languageName}.

      **MANDATORY: Your response must be exclusively the JSON object defined below. Do not add any surrounding text or explanations.**
      **JSON Structure Definition:**
      {
        "elements": [
          { "id": "unique_id_1", "label": "Element Label 1", "type": "entity" },
          { "id": "unique_id_2", "label": "Element Label 2", "type": "concept" }
        ],
        "relations": [
          { "source": "id_of_source_element", "target": "id_of_target_element", "label": "Relationship Label" }
        ],
        "summary": "A brief textual summary in ${languageName} of what this knowledge graph represents for the project."
      }
      (Example structure: elements might include { "id": "e1", "label": "User Authentication", "type": "concept" }, relations might include { "source": "e2", "target": "e1", "label": "implements" })

      **Graph Constraints:**
      -   Element IDs must be simple (e.g., e1, c1).
      -   Include approximately 5-10 elements.
      -   Include approximately 3-7 relations.

      Before finalizing your response, internally review your output to ensure it is valid JSON and meets ALL the critical requirements above. Your response MUST begin with '{' and end with '}'. Output ONLY the JSON text. Nothing else.
    `;

    const suggestionsPrompt = `
      ${basePromptInfo}
      Provide 3-5 pro-active, actionable strategic suggestions for this project. These should be insightful and help guide the project towards success.
      Consider:
      - Potential opportunities to explore.
      - Risks to mitigate.
      - Concrete next steps.
      - Potential synergies (if information allows, otherwise general strategic advice).
      - Creative ideas or novel approaches.

      IMPORTANT: Generate all textual content for "title" and "description" fields in ${languageName}.

      Return *ONLY* a valid JSON array, where each object has: "id" (unique string), "title" (string in ${languageName}), "description" (string in ${languageName}), "type": ("opportunity" | "risk" | "next_step" | "synergy"), and optionally "priority" ("high" | "medium" | "low").
      Strictly adhere to the JSON format. Do not include any comments, explanations, or conversational text within the JSON structure itself or outside of it. The entire response must be *only* the specified JSON array.
      Example: [{ "id": "sugg1", "title": "Title in ${languageName}", "description": "Description in ${languageName}", "type": "opportunity", "priority": "high" }]
    `;

    const assistantPromptsPrompt = `
      ${basePromptInfo}
      Based on the deep analysis of this project, generate a list of 2-3 categories of highly effective, professional AI assistant prompts.
      For each category, provide 2-4 specific prompts. These prompts should be designed for a "Super Assistant" to help the user further develop, analyze, or generate content related to THIS project.

      Each prompt object should have:
      - "id": a unique string identifier (e.g., "cat1_prompt1").
      - "title": A concise, user-friendly title for the prompt in ${languageName}.
      - "description": A brief explanation of what the prompt does or helps achieve, in ${languageName}.
      - "promptTemplate": The actual, engineered prompt text to be sent to an AI model for execution. This template should be in English for optimal AI performance, unless the project content is inherently non-English and requires the prompt in that language. It can include placeholders like {{userInput}} for user-provided text or {{contextInput}} for larger context like file content or existing text.
      - "category": The category name this prompt belongs to (should be one of the category titles you generate).
      - "requiresUserInput": boolean, true if the promptTemplate contains {{userInput}}.
      - "requiresContextInput": boolean, true if the promptTemplate contains {{contextInput}} or is designed to work on a larger piece of provided text.

      The categories should be relevant to the project's nature (e.g., "Content Generation", "Technical Analysis", "Marketing Ideas", "Risk Assessment", "Document Drafting"). Category titles MUST be in ${languageName}.
      The promptTemplates should be "professionally engineered" meaning they are clear, specific, and guide the AI towards high-quality, relevant output for THIS project.

      Return *ONLY* a valid JSON array of AIAssistantPromptCategory objects. Each category object must have "categoryTitle" (string, in ${languageName}) and "prompts" (an array of AIAssistantPrompt objects as described above).
      Strictly adhere to the JSON format. Do not include any comments, explanations, or conversational text within the JSON structure itself or outside of it. The entire response must be *only* the specified JSON array.
      Example:
      [
        {
          "categoryTitle": "Content Generation (${languageName})",
          "prompts": [
            {
              "id": "cg1",
              "title": "Draft Introduction (${languageName})",
              "description": "Generates a draft introduction for a document about this project, based on its core themes. (${languageName})",
              "promptTemplate": "Based on the project '${project.name}' about '${project.description}', and its key themes: {{contextInput}}, draft a compelling introductory paragraph (around 100 words). Focus on engaging the reader and stating the project's main purpose.",
              "category": "Content Generation",
              "requiresUserInput": false,
              "requiresContextInput": true
            }
          ]
        }
      ]
      Ensure the entire response is exclusively this JSON array. No extra text.
    `;

    try {
      const [narrativeResponse, kgResponse, suggestionsResponse, assistantPromptsResponse] = await Promise.all([
        ai.models.generateContent({ model: MODELS.TEXT, contents: [{ role: "user", parts: [{text: narrativePrompt}] }] }),
        ai.models.generateContent({ model: MODELS.TEXT, contents: [{ role: "user", parts: [{text: kgPrompt}] }], config: { responseMimeType: "application/json" } }),
        ai.models.generateContent({ model: MODELS.TEXT, contents: [{ role: "user", parts: [{text: suggestionsPrompt}] }], config: { responseMimeType: "application/json" } }),
        ai.models.generateContent({ model: MODELS.TEXT, contents: [{ role: "user", parts: [{text: assistantPromptsPrompt}] }], config: { responseMimeType: "application/json" } })
      ]);

      const projectPageNarrative = narrativeResponse.text.trim();
      const knowledgeGraph = parseJsonSafe<KnowledgeGraph>(kgResponse.text, "Knowledge Graph") || { elements: [], relations: [], summary: i18n.t('serviceMessages.knowledgeGraphParseErrorSummary', {lng}) };
      const strategicSuggestions = parseJsonSafe<AISuggestion[]>(suggestionsResponse.text, "Strategic Suggestions") || [{id: 'parse_err_sugg', title: i18n.t('serviceMessages.suggestionsParseErrorTitle', {lng}), description: i18n.t('serviceMessages.suggestionsParseErrorDescription', {lng}), type: 'risk'}];
      const suggestedAssistantPrompts = parseJsonSafe<AIAssistantPromptCategory[]>(assistantPromptsResponse.text, "Assistant Prompts") || [];

      let suggestedTemplate = ProjectTemplate.STANDARD;
      const keywordsLower = project.initialAnalysis?.keywords?.map(k => k.toLowerCase()) || [];
      if (keywordsLower.some(k => k.includes(i18n.t('keywords.visual', {lng}).toLowerCase()) || k.includes(i18n.t('keywords.design', {lng}).toLowerCase()) || k.includes(i18n.t('keywords.image', {lng}).toLowerCase()))) {
        suggestedTemplate = ProjectTemplate.VISUAL_HEAVY;
      } else if (keywordsLower.some(k => k.includes(i18n.t('keywords.data', {lng}).toLowerCase()) || k.includes(i18n.t('keywords.report', {lng}).toLowerCase()) || k.includes(i18n.t('keywords.analysis', {lng}).toLowerCase()))) {
        suggestedTemplate = ProjectTemplate.DATA_FOCUSED;
      } else if (projectPageNarrative.length > 150 || keywordsLower.some(k => k.includes(i18n.t('keywords.story', {lng}).toLowerCase()) || k.includes(i18n.t('keywords.narrative', {lng}).toLowerCase()))) {
        suggestedTemplate = ProjectTemplate.NARRATIVE_RICH;
      }

      return {
        projectPageNarrative,
        knowledgeGraph,
        strategicSuggestions,
        suggestedTemplate,
        suggestedAssistantPrompts
      };

    } catch (error) {
      console.error("Error in deep analysis:", error);
      const { summary } = handleApiError(error, lng, 'serviceMessages.deepAnalysisFailedError');

      return {
        projectPageNarrative: summary,
        knowledgeGraph: { elements: [], relations: [], summary: i18n.t('serviceMessages.deepAnalysisFailedError', {error: "KG generation part", lng}) },
        strategicSuggestions: [{ id: 'deep_err', title: i18n.t('serviceMessages.deepAnalysisFailedError', {error: "Suggestions generation part", lng}), description: summary, type: 'risk' }],
        suggestedTemplate: ProjectTemplate.STANDARD,
        suggestedAssistantPrompts: []
      };
    }
  },

  analyzePortfolioSynergies: async (projects: Project[], currentLang: string): Promise<PortfolioAnalysis> => {
    const lng = currentLang;
    const defaultEmptyAnalysis: PortfolioAnalysis = { synergies: [], conflicts: [], sharedResources: [] };
    if (!ai || projects.length < 2) {
      return defaultEmptyAnalysis;
    }

    const languageName = mapLangToName(lng);
    const projectSummaries = projects.map(p =>
      `Project ID: ${p.id}, Name: "${p.name}", Type: ${p.initialAnalysis?.projectTypeGuess || 'N/A'}, Keywords: ${p.initialAnalysis?.keywords?.join(', ') || 'N/A'}, Summary: ${p.initialAnalysis?.summary || p.description.substring(0,100)}`
    ).join('\n---\n');

    const prompt = `
      Analyze the following portfolio of projects to identify potential synergies, conflicts, or shared resources.
      Projects:
      ${projectSummaries}

      Focus on:
      1. Synergies: Where projects could benefit each other (e.g., shared technology, overlapping goals, complementary outputs).
      2. Conflicts: Potential areas of competition, resource contention, or contradictory objectives.
      3. Shared Resources: Opportunities to share knowledge, tools, or personnel across projects.

      IMPORTANT: For each identified synergy, conflict, or shared resource, the "description" field MUST be in ${languageName}. The "resource" name for shared resources can remain in its original language or be a common term.

      Return your response *ONLY* as a valid JSON object with the structure below.
      Strictly adhere to the JSON format. Do not include any comments, explanations, or conversational text within the JSON structure itself or outside of it. The entire response must be *only* the specified JSON object.
      {
        "synergies": [{ "projectIds": ["id1", "id2"], "description": "Description of synergy in ${languageName}" }, ...],
        "conflicts": [{ "projectIds": ["id1", "id2"], "description": "Description of conflict in ${languageName}" }, ...],
        "sharedResources": [{ "resource": "Resource Name", "projectIds": ["id1", "id2", ...], "description": "How it can be shared, in ${languageName}" }, ...]
      }
      Be concise and highlight only the most significant interactions. If none, return empty arrays for each category.
    `;

    try {
      const response: GenerateContentResponse = await ai.models.generateContent({
        model: MODELS.TEXT,
        contents: [{role: "user", parts: [{text: prompt}]}],
        config: { responseMimeType: "application/json" }
      });
      const analysis = parseJsonSafe<PortfolioAnalysis>(response.text, "Portfolio Analysis");

      return {
        synergies: analysis?.synergies || [],
        conflicts: analysis?.conflicts || [],
        sharedResources: analysis?.sharedResources || [],
      };
    } catch (error) {
      console.error("Error analyzing portfolio synergies:", error);
      const { summary } = handleApiError(error, lng, 'serviceMessages.portfolioAnalysisAPIFail');
      return {
        synergies: [],
        conflicts: [{ projectIds: [], description: summary }],
        sharedResources: []
      };
    }
  },

  executeAssistantPrompt: async (promptText: string, contextText: string | undefined, currentLang: string): Promise<string> => {
    const lng = currentLang;
    if (!ai) {
      return i18n.t('serviceMessages.apiKeyMissingErrorSummary', {lng});
    }
    const languageName = mapLangToName(lng);

    let fullPrompt = promptText;
    if(contextText) {
        fullPrompt += `\n\nUse the following text as additional context if relevant and the prompt template indicates it (e.g. contains {{contextInput}}):\n${contextText}`;
    }
    fullPrompt += `\n\nIMPORTANT: Ensure your final response is primarily in ${languageName}, unless the prompt specifically asked for code, technical names, or content that should remain in English.`;

    try {
      const response: GenerateContentResponse = await ai.models.generateContent({
        model: MODELS.TEXT,
        contents: [{ role: "user", parts: [{text: fullPrompt}] }],
      });
      return response.text.trim();
    } catch (error) {
      console.error("Error executing assistant prompt:", error);
      const { summary } = handleApiError(error, lng, 'serviceMessages.assistantExecutionError');
      return summary;
    }
  },

  generateAppIdeaDeveloperPrompt: async (metaPrompt: string, currentLang: string): Promise<string> => {
    const lng = currentLang;
    if (!ai) {
      return i18n.t('serviceMessages.apiKeyMissingErrorSummary', {lng});
    }
    const languageName = mapLangToName(lng);

    const finalMetaPrompt = metaPrompt.replace(/{{outputLanguage}}/g, languageName);

    try {
      const response: GenerateContentResponse = await ai.models.generateContent({
        model: MODELS.TEXT,
        contents: [{role: "user", parts: [{text: finalMetaPrompt}]}],
      });
      return response.text.trim();
    } catch (error) {
      console.error("Error generating app idea developer prompt:", error);
      const { summary } = handleApiError(error, lng, 'serviceMessages.appIdeaGenerationError');
      return summary;
    }
  },

  generateProAppDeveloperPrompt: async (userInput: string, metaPromptTemplate: string, currentLang: string): Promise<string> => {
    const lng = currentLang;
    if (!ai) {
      return i18n.t('serviceMessages.apiKeyMissingErrorSummary', {lng});
    }
    const languageName = mapLangToName(currentLang);

    let finalMetaPrompt = metaPromptTemplate.replace(/{userInput}/g, userInput.replace(/"/g, '\\"'));
    finalMetaPrompt = finalMetaPrompt.replace(/{{outputLanguage}}/g, languageName);

    try {
      const response: GenerateContentResponse = await ai.models.generateContent({
        model: MODELS.TEXT,
        contents: [{role: "user", parts: [{text: finalMetaPrompt}]}],
      });
      return response.text.trim();
    } catch (error) {
      console.error("Error generating Pro App developer prompt:", error);
      const { summary } = handleApiError(error, lng, 'proAppka.generationErrorGeneral');
      return summary;
    }
  },

  generateDynamicAppDeveloperPrompt: async (formData: Record<string, string>, metaPromptTemplate: string, currentLang: string): Promise<string> => {
    const lng = currentLang;
    if (!ai) {
      return i18n.t('serviceMessages.apiKeyMissingErrorSummary', {lng});
    }
    const languageName = mapLangToName(currentLang);

    let formDataString = "";
    for (const key in formData) {
        const labelKey = `dynamicApp.form.fields.${key.toLowerCase()}`;
        const fieldLabel = i18n.t(labelKey, { lng: 'en', defaultValue: key.replace(/_/g, ' ') });

        let valueDisplay = formData[key];
        const optionLabelKey = `dynamicApp.form.options.${key.toLowerCase()}.${formData[key].toLowerCase().replace(/[^a-z0-9_]/gi, '')}`;
        const translatedOption = i18n.t(optionLabelKey, {lng: 'en', defaultValue: formData[key]});
        if (translatedOption !== formData[key] && formData[key]) {
            valueDisplay = `${translatedOption} (key: ${formData[key]})`;
        }
        formDataString += `- ${fieldLabel}: ${valueDisplay}\n`;
    }

    let finalMetaPrompt = metaPromptTemplate.replace('{{formDataString}}', formDataString);
    finalMetaPrompt = finalMetaPrompt.replace(/{{outputLanguage}}/g, languageName);

    try {
      const response: GenerateContentResponse = await ai.models.generateContent({
        model: MODELS.TEXT,
        contents: [{role: "user", parts: [{text: finalMetaPrompt}]}],
      });
      return response.text.trim();
    } catch (error) {
      console.error("Error generating Dynamic App developer prompt:", error);
      const { summary } = handleApiError(error, lng, 'dynamicApp.generationErrorGeneral');
      return summary;
    }
  },

  generateDocumentationOutline: async (blueprintContent: string, currentLang: string): Promise<string> => {
    const lng = currentLang;
    if (!ai) {
      return i18n.t('serviceMessages.apiKeyMissingErrorSummary', {lng});
    }
    const languageName = mapLangToName(currentLang);

    let finalPrompt = DOCUMENTATION_GENERATION_META_PROMPT.replace('{{blueprintContent}}', blueprintContent);
    finalPrompt = finalPrompt.replace(/{{outputLanguage}}/g, languageName);

    try {
      const response: GenerateContentResponse = await ai.models.generateContent({
        model: MODELS.TEXT,
        contents: [{ role: "user", parts: [{text: finalPrompt}] }],
      });
      return response.text.trim();
    } catch (error) {
      console.error("Error generating documentation outline:", error);
      const { summary } = handleApiError(error, lng, 'projectPage.generateDocumentationError');
      return summary;
    }
  },

  generateBlogPost: async (projectInfo: string, currentLang: string): Promise<string> => {
    const lng = currentLang;
    if (!ai) {
      return i18n.t('serviceMessages.apiKeyMissingErrorSummary', {lng});
    }
    const languageName = mapLangToName(currentLang);

    let finalPrompt = BLOG_POST_GENERATION_META_PROMPT.replace('{{projectInfo}}', projectInfo);
    finalPrompt = finalPrompt.replace(/{{outputLanguage}}/g, languageName);

    try {
      const response: GenerateContentResponse = await ai.models.generateContent({
        model: MODELS.TEXT,
        contents: [{ role: "user", parts: [{text: finalPrompt}] }],
      });
      return response.text.trim();
    } catch (error) {
      console.error("Error generating blog post:", error);
      const { summary } = handleApiError(error, lng, 'projectPage.generateBlogPostError');
      return summary;
    }
  },

  generateLandingPageHTML: async (projectInfo: string, currentLang: string): Promise<string> => {
    const lng = currentLang;
    if (!ai) {
      return `<!-- ${i18n.t('serviceMessages.apiKeyMissingErrorSummary', {lng})} -->`;
    }
    const languageName = mapLangToName(lng);
    const htmlLangTag = mapLangToHtmlTag(lng);

    let finalPrompt = LANDING_PAGE_HTML_GENERATION_META_PROMPT.replace('{{projectInfo}}', projectInfo);
    finalPrompt = finalPrompt.replace(/{{outputLanguage}}/g, languageName);
    finalPrompt = finalPrompt.replace(/{{htmlLangTag}}/g, htmlLangTag);

    try {
      const response: GenerateContentResponse = await ai.models.generateContent({
        model: MODELS.TEXT,
        contents: [{ role: "user", parts: [{text: finalPrompt}] }],
      });
      let htmlContent = response.text.trim();
      const htmlFenceRegex = /^```(?:html)?\s*\n?(.*?)\n?\s*```$/si;
      const match = htmlContent.match(htmlFenceRegex);
      if (match && match[1]) {
        htmlContent = match[1].trim();
      }

      if (!htmlContent.toLowerCase().startsWith('<!doctype html>')) {
        console.warn("AI response for landing page doesn't start with <!DOCTYPE html>. Attempting to wrap if it's body content.");
        if (htmlContent.match(/<\w+.*>/) && !htmlContent.match(/<html.*>/i) && !htmlContent.match(/<head.*>/i)){
             htmlContent = `<!DOCTYPE html>
<html lang="${htmlLangTag}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${i18n.t('projectPage.landingPage.defaultTitle', {lng, projectName: projectInfo.split('\n')[0].replace('Project Name: ','') || 'Landing Page'})}</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
    ${htmlContent}
</body>
</html>`;
        } else if (!htmlContent.match(/<\w+.*>/)) {
             console.error("AI response for landing page is not valid HTML. Outputting error message within HTML comments.");
             throw new Error(i18n.t('projectPage.landingPage.generationErrorInvalidHTML', {lng}));
        }
      }

      return htmlContent;
    } catch (error) {
      console.error("Error generating landing page HTML:", error);
      const { summary } = handleApiError(error, lng, 'projectPage.landingPage.generationError');
      return `<!-- ${summary} -->\n<p>${summary}</p>`;
    }
  },

  generateAIDeveloperSpecification: async (blueprintContent: string, currentLang: string): Promise<string> => {
    const lng = currentLang;
    if (!ai) {
      return i18n.t('serviceMessages.apiKeyMissingErrorSummary', {lng});
    }
    const languageName = mapLangToName(currentLang);

    let finalPrompt = AI_AGENT_SPECIFICATION_GENERATION_META_PROMPT.replace('{{blueprintContent}}', blueprintContent);
    finalPrompt = finalPrompt.replace(/{{outputLanguage}}/g, languageName);

    try {
      const response: GenerateContentResponse = await ai.models.generateContent({
        model: MODELS.TEXT,
        contents: [{ role: "user", parts: [{text: finalPrompt}] }],
      });
      return response.text.trim();
    } catch (error) {
      console.error("Error generating AI Developer Specification:", error);
      const { summary } = handleApiError(error, lng, 'projectPage.generateAIDevSpecError');
      return summary;
    }
  },

  generateAIAssistedLogoBrief: async (project: Project, currentLang: string): Promise<Partial<LogoBriefData> | null> => {
    const lng = currentLang;
    if (!ai) {
      console.warn(i18n.t('serviceMessages.apiKeyMissingErrorSummary', {lng}));
      return null;
    }
    const languageName = mapLangToName(lng);
    const projectContext = buildProjectContextForAIFormFill(project, currentLang);

    const prompt = `
      Based on the following project context, help generate suggestions for ALL fields of a creative brief for a logo.
      Project Context:
      ---
      ${projectContext}
      ---

      Your primary goal is to provide a comprehensive set of suggestions.
      The output MUST be a valid JSON object. Ensure all keys from the LogoBriefData structure are present in your response, even if the value is an empty string or a default (like false for boolean) if no specific suggestion can be made.
      Keys to include:
      "brandName": string (e.g., "${project.name}")
      "industry": string (in ${languageName})
      "targetAudience": string (in ${languageName})
      "style": string (Choose one: "modern", "vintage", "minimalist", "playful", "luxury", "traditional", "futuristic", "organic", or "other")
      "styleOther": string (in ${languageName}, if style is "other", otherwise empty string)
      "preferredColors": string (in ${languageName}, e.g., "Modrá, Biela" or "Primary: Dark Blue, Accent: Light Cyan". If unsure, provide an empty string or a general suggestion based on mood/industry.)
      "avoidColors": boolean (true if AI should decide colors or no strong preference from context, false otherwise. Default to false if unsure.)
      "mood": string (Choose one: "trustworthy", "energetic", "calm", "innovative", "professional", "playful_mood", "luxury_mood", "natural", or "other_mood")
      "moodOther": string (in ${languageName}, if mood is "other_mood", otherwise empty string)
      "specificElements": string (in ${languageName}, suggest specific symbols or imagery. If none are obvious, provide an empty string or a general concept like "Abstract geometric shape".)
      "avoidElements": string (in ${languageName}, suggest elements to avoid. If none are obvious, provide an empty string.)

      Ensure all textual descriptions (industry, targetAudience, styleOther, preferredColors, moodOther, specificElements, avoidElements) are in ${languageName}.
      Return *ONLY* a valid JSON object. No other text, explanations, or conversational text. The entire response MUST be this JSON object.
    `;
    try {
      const response: GenerateContentResponse = await ai.models.generateContent({
        model: MODELS.TEXT,
        contents: [{ role: "user", parts: [{text: prompt}] }],
        config: { responseMimeType: "application/json" }
      });
      const brief = parseJsonSafe<Partial<LogoBriefData>>(response.text, "AI Assisted Logo Brief");
      return brief;
    } catch (error) {
      console.error("Error generating AI assisted logo brief:", error);
      const { summary } = handleApiError(error, lng, 'logoGeneration.aiBriefFailedTitle');
      throw new Error(summary);
    }
  },
  
  generateAIAssistedAIDevSpecForm: async (project: Project, currentLang: string): Promise<Partial<AIDevSpecFormData>> => {
    const lng = currentLang;
    if (!ai) throw new Error(i18n.t('serviceMessages.apiKeyMissingErrorSummary', {lng}));
    
    const languageName = mapLangToName(lng);
    const projectContext = buildProjectContextForAIFormFill(project, currentLang);

    const prompt = `
      Analyze the provided project context and suggest comprehensive content for ALL fields of an AI Developer Specification form.
      Project Context:
      ---
      ${projectContext}
      ---
      
      Your output MUST be a single, valid JSON object. This object should contain keys corresponding to the AIDevSpecFormData structure.
      Provide suggestions for ALL the following fields, ensuring all keys are present. If a suggestion cannot be made for a field, use an empty string "" or a sensible default.
      "appName": string (e.g., "${project.name}")
      "mainPurpose": string (in ${languageName})
      "industry": string (in ${languageName})
      "targetAudience": string (in ${languageName})
      "usp": string (Unique Selling Proposition, in ${languageName})
      "coreFeatures": string (List of features, in ${languageName})
      "criticalRequirements": string (e.g., Security, scalability, in ${languageName})
      "techStackHighLevel": string (Preferred tech, in ${languageName}, technical terms can be English)
      "architectureType": string (Choose one: "monolith", "microservices", "serverless", "other_arch", or empty string if unsure)
      "userFlows": string (Key user interactions, in ${languageName})
      "uiUxHighLevel": string (Design ideas, in ${languageName})
      "implementationDeployment": string (CI/CD, hosting, in ${languageName}, technical terms can be English)
      "otherRequirements": string (Testing, docs, in ${languageName})

      Ensure all textual descriptions are in ${languageName}, unless they are technical terms like framework names.
      Return *ONLY* the valid JSON object. No other text, explanations, or markdown.
    `;
    try {
      const response: GenerateContentResponse = await ai.models.generateContent({ model: MODELS.TEXT, contents: [{ role: "user", parts: [{text: prompt}] }], config: { responseMimeType: "application/json" } });
      return parseJsonSafe<Partial<AIDevSpecFormData>>(response.text, "AI Assisted AI Dev Spec Form") || {};
    } catch (error) {
      console.error("Error generating AI assisted AI Dev Spec form:", error);
      const { summary } = handleApiError(error, lng, 'notifications.aiAssistedFormFillErrorTitle');
      throw new Error(summary);
    }
  },

  generateAIAssistedBlogPostForm: async (project: Project, currentLang: string): Promise<Partial<BlogPostFormData>> => {
    const lng = currentLang;
    if (!ai) throw new Error(i18n.t('serviceMessages.apiKeyMissingErrorSummary', {lng}));

    const languageName = mapLangToName(lng);
    const projectContext = buildProjectContextForAIFormFill(project, currentLang);
    
    const prompt = `
      Analyze the provided project context and suggest comprehensive content for ALL fields of a Blog Post form.
      Project Context:
      ---
      ${projectContext}
      ---

      Your output MUST be a single, valid JSON object. This object should contain keys corresponding to the BlogPostFormData structure.
      Provide suggestions for ALL the following fields, ensuring all keys are present. If a suggestion cannot be made, use an empty string "" or a sensible default (like "medium" for postLength).
      "topic": string (e.g., "${project.name}", in ${languageName})
      "postGoal": string (Choose: "educate", "promote", "inform", "other_goal", or empty)
      "postLength": string (Choose: "short", "medium", "long", or empty; default to "medium")
      "targetAudienceBlog": string (in ${languageName})
      "tone": string (Choose: "formal", "friendly", "technical", "motivational", "other_tone", or empty; default to "friendly")
      "keyMessages": string (CSV or descriptive text, in ${languageName})
      "preferredKeywords": string (CSV, in ${languageName} or English if technical)
      "postStructure": string (Suggested sections, in ${languageName})
      "ctaText": string (in ${languageName})
      "ctaLink": string (URL)

      Ensure all textual descriptions are in ${languageName}.
      Return *ONLY* the valid JSON object. No other text, explanations, or markdown.
    `;
    try {
      const response: GenerateContentResponse = await ai.models.generateContent({ model: MODELS.TEXT, contents: [{ role: "user", parts: [{text: prompt}] }], config: { responseMimeType: "application/json" } });
      return parseJsonSafe<Partial<BlogPostFormData>>(response.text, "AI Assisted Blog Post Form") || {};
    } catch (error) {
      console.error("Error generating AI assisted Blog Post form:", error);
      const { summary } = handleApiError(error, lng, 'notifications.aiAssistedFormFillErrorTitle');
      throw new Error(summary);
    }
  },

  generateAIAssistedLandingPageForm: async (project: Project, currentLang: string): Promise<Partial<LandingPageFormData>> => {
    const lng = currentLang;
    if (!ai) throw new Error(i18n.t('serviceMessages.apiKeyMissingErrorSummary', {lng}));

    const languageName = mapLangToName(lng);
    const projectContext = buildProjectContextForAIFormFill(project, currentLang);
    const currentYear = new Date().getFullYear();
    
    const prompt = `
      Analyze the provided project context and suggest comprehensive content for ALL fields of a Landing Page form.
      Project Context:
      ---
      ${projectContext}
      ---

      Your output MUST be a single, valid JSON object. This object should contain keys corresponding to the LandingPageFormData structure.
      Provide suggestions for ALL the following fields, ensuring all keys are present. If a suggestion cannot be made, use an empty string "", an empty array [], an empty object {}, or a sensible default.
      "pageName": string (e.g., "${project.name} Landing Page", in ${languageName})
      "mainPurposePage": string (in ${languageName})
      "targetAudiencePage": string (in ${languageName})
      "keyMessagesUSP": string (in ${languageName})
      "designStyle": string (Choose: "modern", "minimalist", "luxury", "playful_lp", "other_style_lp", or empty; default to "modern")
      "primaryColor": string (HEX or descriptive, e.g., "#3B82F6" or "Deep Blue")
      "secondaryColor": string (HEX or descriptive, e.g., "#10B981" or "Vibrant Green")
      "typographyPreference": string (Choose: "serif", "sans-serif", "mixed", "other_typo", or empty; default to "sans-serif")
      "requiredSections": string[] (Array of strings, e.g., ["Hero", "Benefits", "Features", "Testimonials", "FAQ", "Pricing", "Contact"], in ${languageName} or English for common terms; default to ["Hero", "Features", "Benefits", "CTA"])
      "sectionContentHints": object (Object where keys are section names from 'requiredSections' and values are content hints in ${languageName}, e.g., {"Hero": "Focus on innovation.", "Features": "Detail X, Y, Z."}; default to empty object {})
      "ctaTextPage": string (in ${languageName})
      "ctaLinkPage": string (URL)
      "animationPreference": string (Choose: "none_anim", "subtle", "dynamic", or empty; default "subtle")
      "navMenuContent": string (CSV, e.g., "Home, About, Services, Blog, Contact", in ${languageName})
      "footerContent": string (e.g., "© ${currentYear} ${project.name}. All rights reserved.", in ${languageName})
      "seoKeywords": string (CSV, in ${languageName} or English)
      "metaTitle": string (in ${languageName})
      "metaDescription": string (in ${languageName})

      Ensure all textual descriptions are in ${languageName}. For 'requiredSections', use common English terms like "Hero", "Features", etc., or provide ${languageName} equivalents.
      Return *ONLY* the valid JSON object. No other text, explanations, or markdown.
    `;
    try {
      const response: GenerateContentResponse = await ai.models.generateContent({ model: MODELS.TEXT, contents: [{ role: "user", parts: [{text: prompt}] }], config: { responseMimeType: "application/json" } });
      const parsedData = parseJsonSafe<Partial<LandingPageFormData>>(response.text, "AI Assisted Landing Page Form");
      
      if (parsedData) {
        if (typeof parsedData.requiredSections === 'string') {
          parsedData.requiredSections = (parsedData.requiredSections as unknown as string).split(',').map(s => s.trim()).filter(s => s);
        } else if (!Array.isArray(parsedData.requiredSections)) {
            parsedData.requiredSections = ['Hero', 'Features', 'Benefits', 'CTA']; 
        }

        if (typeof parsedData.sectionContentHints === 'string') {
            console.warn("AI returned sectionContentHints as a string. Manual formatting might be needed.", parsedData.sectionContentHints);
        } else if (typeof parsedData.sectionContentHints !== 'object' || parsedData.sectionContentHints === null) {
            parsedData.sectionContentHints = {};
        }
      }
      return parsedData || {};

    } catch (error) {
      console.error("Error generating AI assisted Landing Page form:", error);
      const { summary } = handleApiError(error, lng, 'notifications.aiAssistedFormFillErrorTitle');
      throw new Error(summary);
    }
  },


  generateLogoFromBrief: async (brief: LogoBriefData, project: Project, currentLang: string): Promise<GeneratedImageDetails | undefined> => {
    const lng = currentLang;
    if (!ai) {
        const errorMsg = i18n.t('serviceMessages.apiKeyMissingErrorSummary', {lng});
        console.warn(errorMsg);
        throw new Error(errorMsg);
    }

    let imagePrompt = `Professional logo for: ${brief.brandName}. Industry: ${brief.industry}. `;
    imagePrompt += `Target Audience: ${brief.targetAudience}. Style: ${brief.style}${brief.styleOther ? ` (${brief.styleOther})` : ''}. `;
    if (brief.avoidColors) {
      imagePrompt += `Colors: AI to choose appropriate colors based on brief. `;
    } else {
      imagePrompt += `Preferred Colors: ${brief.preferredColors}. `;
    }
    imagePrompt += `Mood: ${brief.mood}${brief.moodOther ? ` (${brief.moodOther})` : ''}. `;
    if (brief.specificElements) imagePrompt += `Incorporate elements like: ${brief.specificElements}. `;
    if (brief.avoidElements) imagePrompt += `Avoid elements like: ${brief.avoidElements}. `;
    imagePrompt += `Additional project context: ${project.description.substring(0, 100)}. `;
    imagePrompt += `Design as a clean, modern, vector-style logo, suitable for digital use, isolated on a white or transparent background. Iconic and memorable.`;

    try {
      const response: GenerateImagesResponse = await withRetry(() => ai.models.generateImages({
        model: MODELS.IMAGE,
        prompt: imagePrompt,
        config: { numberOfImages: 1, outputMimeType: 'image/jpeg' },
      }));
      if (response.generatedImages && response.generatedImages.length > 0) {
        const base64ImageBytes = response.generatedImages[0].image.imageBytes;
        return { url: `data:image/jpeg;base64,${base64ImageBytes}`, prompt: imagePrompt, alternatives: [] };
      }
      return undefined;
    } catch (error) {
      console.error("Error generating logo from brief:", error);
      const { summary } = handleApiError(error, lng, 'serviceMessages.imageGenerationError');
      throw new Error(summary);
    }
  },

  generateGenericAsset: async (
    project: Project,
    formData: AIDevSpecFormData | BlogPostFormData | LandingPageFormData,
    assetType: 'AIDeveloperSpec' | 'BlogPost' | 'LandingPageContentAndVisualSpec',
    currentLang: string
  ): Promise<string> => {
    const lng = currentLang;
    if (!ai) {
      throw new Error(i18n.t('serviceMessages.apiKeyMissingErrorSummary', {lng}));
    }
    const languageName = mapLangToName(lng);
    const htmlLangTag = mapLangToHtmlTag(lng);

    let metaPrompt = GENERIC_ASSET_GENERATION_META_PROMPT;
    let outputSpecifications = "";
    let assetTypeDisplay = "";
    let additionalContext = "";

    const projectContext = `
      Project Name: ${project.name}
      Project Description: ${project.description}
      Initial Analysis - Keywords: ${project.initialAnalysis?.keywords?.join(', ') || 'N/A'}
      Initial Analysis - Type Guess: ${project.initialAnalysis?.projectTypeGuess || 'N/A'}
      Initial Analysis - Summary: ${project.initialAnalysis?.summary || 'N/A'}
    `;
    additionalContext += projectContext;

    const defaultIndustry = project.initialAnalysis?.projectTypeGuess || 'General';
    const defaultTargetAudience = 'General Audience';
    const defaultUSP = project.initialAnalysis?.summary || 'Unique value proposition';
    const defaultAppName = project.name;


    let currentYear = new Date().getFullYear();

    switch(assetType) {
      case 'AIDeveloperSpec':
        const devSpecData = formData as AIDevSpecFormData;
        assetTypeDisplay = "AI Developer Specification";
        outputSpecifications = AI_DEVELOPER_SPEC_OUTPUT_SPECS;
        for (const key in devSpecData) {
          const typedKey = key as keyof AIDevSpecFormData;
          outputSpecifications = outputSpecifications.replace(new RegExp(`{{${typedKey}}}`, 'g'), devSpecData[typedKey] || 'N/A');
          additionalContext += `\n${typedKey.replace(/([A-Z])/g, ' $1').trim()}: ${devSpecData[typedKey] || 'N/A'}`;
        }
        outputSpecifications = outputSpecifications.replace(new RegExp(`{{appName}}`, 'g'), devSpecData.appName || defaultAppName);
        outputSpecifications = outputSpecifications.replace(new RegExp(`{{mainPurpose}}`, 'g'), devSpecData.mainPurpose || project.description);
        outputSpecifications = outputSpecifications.replace(new RegExp(`{{targetAudience}}`, 'g'), devSpecData.targetAudience || defaultTargetAudience);
        outputSpecifications = outputSpecifications.replace(new RegExp(`{{techStackHighLevel}}`, 'g'), devSpecData.techStackHighLevel || 'Any modern stack');
        outputSpecifications = outputSpecifications.replace(new RegExp(`{{architectureType}}`, 'g'), devSpecData.architectureType || 'Not specified');
        outputSpecifications = outputSpecifications.replace(new RegExp(`{{coreFeatures}}`, 'g'), devSpecData.coreFeatures || 'Core features to be defined');
        outputSpecifications = outputSpecifications.replace(new RegExp(`{{uiUxHighLevel}}`, 'g'), devSpecData.uiUxHighLevel || 'Clean and intuitive');
        outputSpecifications = outputSpecifications.replace(new RegExp(`{{criticalRequirements}}`, 'g'), devSpecData.criticalRequirements || 'Standard security and performance');
        outputSpecifications = outputSpecifications.replace(new RegExp(`{{implementationDeployment}}`, 'g'), devSpecData.implementationDeployment || 'Standard cloud deployment');
        outputSpecifications = outputSpecifications.replace(new RegExp(`{{otherRequirements}}`, 'g'), devSpecData.otherRequirements || 'None specified');
        break;
      case 'BlogPost':
        const blogData = formData as BlogPostFormData;
        assetTypeDisplay = "Blog Post";
        outputSpecifications = BLOG_POST_OUTPUT_SPECS;
         for (const key in blogData) {
          const typedKey = key as keyof BlogPostFormData;
          outputSpecifications = outputSpecifications.replace(new RegExp(`{{${typedKey}}}`, 'g'), blogData[typedKey] || 'N/A');
          additionalContext += `\n${typedKey.replace(/([A-Z])/g, ' $1').trim()}: ${blogData[typedKey] || 'N/A'}`;
        }
        outputSpecifications = outputSpecifications.replace(new RegExp(`{{topic}}`, 'g'), blogData.topic || project.name);
        break;
      case 'LandingPageContentAndVisualSpec':
        const lpData = formData as LandingPageFormData;
        assetTypeDisplay = "Landing Page Content and Visual Specification";
        outputSpecifications = LANDING_PAGE_CONTENT_AND_VISUAL_SPEC_OUTPUT_SPECS;
        for (const key in lpData) {
          const typedKey = key as keyof LandingPageFormData;
          if (Array.isArray(lpData[typedKey])) {
             outputSpecifications = outputSpecifications.replace(new RegExp(`{{${typedKey}}}`, 'g'), (lpData[typedKey] as string[]).join(', ') || 'N/A');
          } else if (typeof lpData[typedKey] === 'object' && lpData[typedKey] !== null && typedKey === 'sectionContentHints') {
            let hintsString = "";
            for (const sectionKey in lpData[typedKey] as Record<string, string>) {
                hintsString += `\n  - ${sectionKey}: ${(lpData[typedKey] as Record<string, string>)[sectionKey]}`;
            }
            outputSpecifications = outputSpecifications.replace(new RegExp(`{{${typedKey}}}`, 'g'), hintsString || 'N/A');
          } else {
             outputSpecifications = outputSpecifications.replace(new RegExp(`{{${typedKey}}}`, 'g'), String(lpData[typedKey] || 'N/A'));
          }
          additionalContext += `\n${typedKey.replace(/([A-Z])/g, ' $1').trim()}: ${String(lpData[typedKey] || 'N/A')}`;
        }
        outputSpecifications = outputSpecifications.replace(new RegExp(`{{currentYear}}`, 'g'), currentYear.toString());
        outputSpecifications = outputSpecifications.replace(new RegExp(`{{pageName}}`, 'g'), lpData.pageName || project.name);

        if (Array.isArray(lpData.requiredSections)) {
            const sectionsString = lpData.requiredSections.join(', ');
            outputSpecifications = outputSpecifications.replace(new RegExp(`{{requiredSections}}`, 'g'), sectionsString || 'Hero, Features, CTA');
        } else {
            outputSpecifications = outputSpecifications.replace(new RegExp(`{{requiredSections}}`, 'g'), 'Hero, Features, CTA');
        }
        break;
    }

    metaPrompt = metaPrompt.replace('{{assetTypeDisplay}}', assetTypeDisplay);
    metaPrompt = metaPrompt.replace(/{{outputLanguage}}/g, languageName);
    metaPrompt = metaPrompt.replace('{{htmlLangTag}}', htmlLangTag);
    metaPrompt = metaPrompt.replace('{{additionalContext}}', additionalContext);
    metaPrompt = metaPrompt.replace('{{outputSpecifications}}', outputSpecifications);

    metaPrompt = metaPrompt.replace('{{projectName}}', project.name);
    metaPrompt = metaPrompt.replace('{{projectDescription}}', project.description);
    metaPrompt = metaPrompt.replace('{{projectIndustry}}', (formData as any).industry || defaultIndustry);
    metaPrompt = metaPrompt.replace('{{projectTargetAudience}}', (formData as any).targetAudience || defaultTargetAudience);
    metaPrompt = metaPrompt.replace('{{projectUSP}}', (formData as any).usp || (formData as any).keyMessagesUSP || defaultUSP);


    try {
      const response: GenerateContentResponse = await ai.models.generateContent({
        model: MODELS.TEXT,
        contents: [{ role: "user", parts: [{text: metaPrompt}] }],
      });
      let content = response.text.trim();

      if (assetType === 'AIDeveloperSpec' || assetType === 'BlogPost' || assetType === 'LandingPageContentAndVisualSpec') {
        const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
        const match = content.match(fenceRegex);
        if (match && match[2]) {
          content = match[2].trim();
        }
      }
      return content;
    } catch (error) {
      console.error(`Error generating ${assetType}:`, error);
      const { summary } = handleApiError(error, lng, 'serviceMessages.assetGenerationError');
      throw new Error(summary);
    }
  },
};
