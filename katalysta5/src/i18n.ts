
import i18next from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Async function to fetch translation files
async function fetchTranslations() {
  const enPath = '/src/locales/en/translation.json';
  const skPath = '/src/locales/sk/translation.json';
  try {
    const [enResponse, skResponse] = await Promise.all([
      fetch(enPath),
      fetch(skPath)
    ]);

    let enTranslationsJson;
    let skTranslationsJson;

    // Process English translations
    if (!enResponse.ok) {
      const failedUrl = enResponse.url || enPath;
      throw new Error(`Failed to fetch enTranslations: ${enResponse.statusText} (URL: ${failedUrl}, Status: ${enResponse.status})`);
    }
    const enContentType = enResponse.headers.get('content-type');
    if (enContentType && enContentType.includes('application/json')) {
      enTranslationsJson = await enResponse.json();
    } else {
      const responseText = await enResponse.text();
      console.warn(`enTranslations: Content-Type header was '${enContentType || 'unknown'}', not 'application/json'. Attempting to parse as JSON. Response text (first 500 chars): ${responseText.substring(0,500)}...`);
      try {
        enTranslationsJson = JSON.parse(responseText);
        // If successful, we've recovered.
      } catch (parseError) {
        // If parsing fails, it truly wasn't JSON or was malformed.
        console.error(`enTranslations: Failed to parse response as JSON after incorrect Content-Type. Error: ${parseError}. Original Content-Type: '${enContentType || 'unknown'}'`);
        throw new Error(`enTranslations: Incorrect content-type ('${enContentType || 'unknown'}') and failed to parse body as JSON.`);
      }
    }

    // Process Slovak translations
    if (!skResponse.ok) {
      const failedUrl = skResponse.url || skPath;
      throw new Error(`Failed to fetch skTranslations: ${skResponse.statusText} (URL: ${failedUrl}, Status: ${skResponse.status})`);
    }
    const skContentType = skResponse.headers.get('content-type');
    if (skContentType && skContentType.includes('application/json')) {
      skTranslationsJson = await skResponse.json();
    } else {
      const responseText = await skResponse.text();
      console.warn(`skTranslations: Content-Type header was '${skContentType || 'unknown'}', not 'application/json'. Attempting to parse as JSON. Response text (first 500 chars): ${responseText.substring(0,500)}...`);
      try {
        skTranslationsJson = JSON.parse(responseText);
        // If successful, we've recovered.
      } catch (parseError) {
        // If parsing fails, it truly wasn't JSON or was malformed.
        console.error(`skTranslations: Failed to parse response as JSON after incorrect Content-Type. Error: ${parseError}. Original Content-Type: '${skContentType || 'unknown'}'`);
        throw new Error(`skTranslations: Incorrect content-type ('${skContentType || 'unknown'}') and failed to parse body as JSON.`);
      }
    }

    return {
      en: { translation: enTranslationsJson },
      sk: { translation: skTranslationsJson },
    };
  } catch (error) { // This catch block handles fetch errors, .ok false errors, and our thrown content-type/parse errors
    console.error("Error loading translation files:", error);
    // Fallback to empty translations in case of an error
    return {
      en: { translation: { /* Consider adding a few critical fallback keys here if needed */ } },
      sk: { translation: { /* Consider adding a few critical fallback keys here if needed */ } },
    };
  }
}

// Export a promise that resolves when i18next is initialized
export const i18nPromise = fetchTranslations().then(resources => {
  return i18next
    .use(initReactI18next)
    .use(LanguageDetector)
    .init({
      resources,
      lng: 'sk', // Set Slovak as the default language
      fallbackLng: 'en',
      interpolation: {
        escapeValue: false, // React already protects from XSS
      },
      detection: {
        order: ['localStorage', 'navigator', 'htmlTag'],
        caches: ['localStorage'],
      },
      debug: false, // Set to true for debugging
    });
}).catch(error => {
  console.error("Major error during i18next initialization:", error);
  // It's crucial that i18next still initializes, even with no resources,
  // so the app doesn't crash. The UI will show translation keys.
  return i18next
    .use(initReactI18next)
    .init({
        resources: {
            en: { translation: {} },
            sk: { translation: {} }
        },
        lng: 'sk',
        fallbackLng: 'en',
        interpolation: { escapeValue: false },
        debug: false
    });
});

export default i18next;
