
import type { LogoBriefData, LogoStyleOptionValue, LogoMoodOptionValue } from '../../types';

// Types for Logo Generation Form
export interface LogoFormFieldOption {
  value: LogoStyleOptionValue | LogoMoodOptionValue | string;
  labelKey: string;
}

export interface LogoFormFieldDefinition {
  name: keyof LogoBriefData;
  labelKey: string;
  type: 'text' | 'textarea' | 'select' | 'checkbox';
  tooltipKey: string;
  placeholderKey?: string;
  required?: boolean;
  options?: LogoFormFieldOption[];
  defaultValue?: string | boolean;
  isColorInput?: boolean;
}

// Types for Dynamic App Form (used by src/config/dynamicAppFormConfig.ts)
export interface DynamicAppFormOption {
  value: string;
  labelKey: string;
}

export interface DynamicAppFormFieldDefinition {
  name: string; // This will be the key in formData
  labelKey: string; // For translation
  type: 'select' | 'text' | 'textarea';
  options?: DynamicAppFormOption[];
  defaultValue?: string;
  sectionKey: string; // To group fields under a section title (translation key)
  tooltipKey: string; // For translation of help tooltip
  placeholderKey?: string; // For translation of input placeholder
  required?: boolean;
}
