
import type { LogoFormFieldDefinition } from './formTypes';

export const LOGO_BRIEF_FORM_CONFIG: LogoFormFieldDefinition[] = [
  { name: 'brandName', labelKey: 'logoGeneration.form.brandName.label', type: 'text', tooltipKey: 'logoGeneration.form.brandName.tooltip', placeholderKey: 'logoGeneration.form.brandName.placeholder', required: true },
  { name: 'industry', labelKey: 'logoGeneration.form.industry.label', type: 'text', tooltipKey: 'logoGeneration.form.industry.tooltip', placeholderKey: 'logoGeneration.form.industry.placeholder', required: true },
  { name: 'targetAudience', labelKey: 'logoGeneration.form.targetAudience.label', type: 'textarea', tooltipKey: 'logoGeneration.form.targetAudience.tooltip', placeholderKey: 'logoGeneration.form.targetAudience.placeholder', required: true },
  {
    name: 'style', labelKey: 'logoGeneration.form.style.label', type: 'select', tooltipKey: 'logoGeneration.form.style.tooltip',
    options: [
      { value: 'modern', labelKey: 'logoGeneration.form.style.options.modern' }, { value: 'vintage', labelKey: 'logoGeneration.form.style.options.vintage' },
      { value: 'minimalist', labelKey: 'logoGeneration.form.style.options.minimalist' }, { value: 'playful', labelKey: 'logoGeneration.form.style.options.playful' },
      { value: 'luxury', labelKey: 'logoGeneration.form.style.options.luxury' }, { value: 'traditional', labelKey: 'logoGeneration.form.style.options.traditional' },
      { value: 'futuristic', labelKey: 'logoGeneration.form.style.options.futuristic' }, { value: 'organic', labelKey: 'logoGeneration.form.style.options.organic' },
      { value: 'other', labelKey: 'logoGeneration.form.style.options.other' }
    ],
    defaultValue: 'modern',
  },
  { name: 'styleOther', labelKey: 'logoGeneration.form.styleOther.label', type: 'text', tooltipKey: 'logoGeneration.form.styleOther.tooltip', placeholderKey: 'logoGeneration.form.styleOther.placeholder' },
  { name: 'preferredColors', labelKey: 'logoGeneration.form.preferredColors.label', type: 'text', tooltipKey: 'logoGeneration.form.preferredColors.tooltip', placeholderKey: 'logoGeneration.form.preferredColors.placeholder', isColorInput: true },
  { name: 'avoidColors', labelKey: 'logoGeneration.form.avoidColors.label', type: 'checkbox', tooltipKey: 'logoGeneration.form.avoidColors.tooltip', defaultValue: false },
  {
    name: 'mood', labelKey: 'logoGeneration.form.mood.label', type: 'select', tooltipKey: 'logoGeneration.form.mood.tooltip',
    options: [
      { value: 'trustworthy', labelKey: 'logoGeneration.form.mood.options.trustworthy' }, { value: 'energetic', labelKey: 'logoGeneration.form.mood.options.energetic' },
      { value: 'calm', labelKey: 'logoGeneration.form.mood.options.calm' }, { value: 'innovative', labelKey: 'logoGeneration.form.mood.options.innovative' },
      { value: 'professional', labelKey: 'logoGeneration.form.mood.options.professional' }, { value: 'playful_mood', labelKey: 'logoGeneration.form.mood.options.playful_mood' },
      { value: 'luxury_mood', labelKey: 'logoGeneration.form.mood.options.luxury_mood' }, { value: 'natural', labelKey: 'logoGeneration.form.mood.options.natural' },
      { value: 'other_mood', labelKey: 'logoGeneration.form.mood.options.other_mood' }
    ],
    defaultValue: 'innovative',
  },
  { name: 'moodOther', labelKey: 'logoGeneration.form.moodOther.label', type: 'text', tooltipKey: 'logoGeneration.form.moodOther.tooltip', placeholderKey: 'logoGeneration.form.moodOther.placeholder' },
  { name: 'specificElements', labelKey: 'logoGeneration.form.specificElements.label', type: 'textarea', tooltipKey: 'logoGeneration.form.specificElements.tooltip', placeholderKey: 'logoGeneration.form.specificElements.placeholder' },
  { name: 'avoidElements', labelKey: 'logoGeneration.form.avoidElements.label', type: 'textarea', tooltipKey: 'logoGeneration.form.avoidElements.tooltip', placeholderKey: 'logoGeneration.form.avoidElements.placeholder' },
];
