/* Basic CSS reset and global styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', sans-serif;
}

/* Code styling improvements */
.markdown-content code {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', '<PERSON>sol<PERSON>', 'Courier New', monospace !important;
  font-weight: 500;
  letter-spacing: 0.025em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.markdown-content pre {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', 'Courier New', monospace !important;
  line-height: 1.6;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.markdown-content pre code {
  background: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Enhanced code block styling */
.code-block {
  position: relative;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

.inline-code {
  background: linear-gradient(135deg, #2d2d2d 0%, #404040 100%) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Improve text rendering for code */
.markdown-content code,
.markdown-content pre {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-variant-ligatures: none;
}

/* Additional global styles can be added here if needed */
