
import React from 'react';
import { ICONS } from '../constants';

const HowAIWorksPage: React.FC = () => {
  return (
    <div className="bg-base-100 p-6 sm:p-8 rounded-lg shadow-xl prose prose-indigo max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-primary mb-6 flex items-center">
        {ICONS.AI_SPARKLE} <span className="ml-3">Our AI: Your Project Supercharger</span>
      </h1>

      <p className="lead">
        The AI Project Catalyst leverages cutting-edge generative AI to transform your project management experience. It's designed not just to assist, but to actively co-create, strategize, and inspire. Here's a glimpse into how our AI works and learns:
      </p>

      <h2 className="text-2xl font-semibold text-neutral mt-8">Core AI Capabilities</h2>
      <ul>
        <li>
          <strong>Intelligent Data Ingestion & Preliminary Analysis:</strong> When you create a project and provide initial information (name, description, conceptual files), the AI performs a quick scan. It identifies potential project types, extracts key themes and keywords, and provides an initial summary. This sets the stage for deeper understanding.
        </li>
        <li>
          <strong>Deep Semantic Analysis & Knowledge Graph Construction:</strong> At your command, the AI dives deep into all provided project data. It performs semantic analysis to understand the meaning, context, and relationships within your information. This process helps build a conceptual knowledge graph, highlighting key entities, concepts, and their interconnections.
        </li>
        <li>
          <strong>Generative Content Creation:</strong>
          <ul>
            <li><strong>Project Narratives:</strong> Based on its analysis, the AI crafts compelling, "living" narratives that articulate your project's vision, objectives, and potential impact.</li>
            <li><strong>Visual Inspiration:</strong> It generates unique, symbolic images to visually represent the essence of your project, sparking creativity and providing a visual anchor.</li>
            <li><strong>Strategic Suggestions:</strong> The AI acts as a consultant, offering actionable advice, identifying potential risks and opportunities, and proposing next steps.</li>
          </ul>
        </li>
        <li>
          <strong>Portfolio-Level Intelligence:</strong> The AI can analyze your entire project portfolio to identify potential synergies between projects, flag possible conflicts, and suggest opportunities for sharing resources or knowledge. This holistic view helps optimize your overall efforts.
        </li>
      </ul>

      <h2 className="text-2xl font-semibold text-neutral mt-8">How the AI Learns and Improves (Conceptual)</h2>
      <p>
        While this demonstration operates with pre-trained models, a fully realized AI Project Catalyst would incorporate mechanisms for continuous learning and adaptation:
      </p>
      <ul>
        <li>
          <strong>Interaction Feedback:</strong> The AI would learn from how you interact with its suggestions. If you consistently adopt certain types of advice or modify its generated content in specific ways, it would adjust its future outputs to better align with your preferences and project needs.
        </li>
        <li>
          <strong>Project Outcomes (Long-term):</strong> By (conceptually) linking project data to their eventual success or failure metrics (with user consent and privacy paramount), the AI could learn to identify patterns that correlate with positive outcomes. This would allow it to refine its strategic advice over time, becoming more predictive and insightful. For example, it might learn that projects with certain keyword combinations or structural properties in their knowledge graphs tend to be more successful in specific domains.
        </li>
        <li>
          <strong>Template Adaptation:</strong> If users frequently customize generated project pages or select specific display templates for certain types of content, the AI could learn to suggest more appropriate templates by default.
        </li>
        <li>
          <strong>Domain-Specific Knowledge Refinement:</strong> For organizations using the tool extensively within a particular industry, the AI could (with appropriate fine-tuning or retrieval-augmented generation techniques) become more adept at understanding and generating content specific to that domain's jargon, common challenges, and best practices.
        </li>
        <li>
          <strong>User-Guided Refinement:</strong> Features allowing users to explicitly rate the usefulness of AI suggestions or provide corrective feedback would directly contribute to the AI's learning loop, making it a more personalized and effective partner.
        </li>
      </ul>
      <p>
        The goal is an AI that evolves alongside you and your projects, becoming an increasingly indispensable catalyst for innovation and success. It's about augmenting human intelligence, not replacing it, to help you achieve your most ambitious visions.
      </p>

      <div className="mt-10 p-4 bg-primary/10 border border-primary/20 rounded-lg">
        <h3 className="text-xl font-semibold text-primary flex items-center">
          {ICONS.LIGHTBULB} <span className="ml-2">The Vision: A True Co-Creator</span>
        </h3>
        <p className="mt-2 text-neutral/90">
          Imagine an AI that not only analyzes data but also helps draft technical specifications, brainstorms marketing slogans, suggests code snippets, or even helps outline presentation slides. This is the direction we are heading – an AI that's deeply integrated into the creative and strategic fabric of your work, making project development faster, smarter, and more impactful.
        </p>
      </div>
    </div>
  );
};

export default HowAIWorksPage;
    