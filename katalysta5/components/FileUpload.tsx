
import React, { useState, useCallback } from 'react';
import { ProjectFile } from '../types';
import { ICONS, FILE_TYPES_SUPPORTED } from '../constants';
import { v4 as uuidv4 } from 'uuid';

interface FileUploadProps {
  onFilesUploaded: (files: ProjectFile[]) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ onFilesUploaded }) => {
  const [selectedFiles, setSelectedFiles] = useState<ProjectFile[]>([]);

  // This simulates file selection as actual file input is complex for this demo
  const handleSimulatedFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const newProjectFiles: ProjectFile[] = files.map(file => ({
      id: uuidv4(),
      name: file.name,
      type: file.type || 'unknown',
      size: file.size,
    }));
    
    // For demo, let's add a few conceptual files if none are selected via input
    if (newProjectFiles.length === 0 && selectedFiles.length === 0) {
        const conceptualFiles: ProjectFile[] = [
            { id: uuidv4(), name: 'Project_Brief.pdf', type: 'application/pdf', size: 1024 * 250 },
            { id: uuidv4(), name: 'Initial_Data.json', type: 'application/json', size: 1024 * 50 },
            { id: uuidv4(), name: 'User_Research_Notes.txt', type: 'text/plain', size: 1024 * 15 },
        ];
        setSelectedFiles(conceptualFiles);
        onFilesUploaded(conceptualFiles);
    } else {
        const allFiles = [...selectedFiles, ...newProjectFiles];
        setSelectedFiles(allFiles);
        onFilesUploaded(allFiles);
    }
  }, [onFilesUploaded, selectedFiles]);

  const removeFile = (fileId: string) => {
    const updatedFiles = selectedFiles.filter(f => f.id !== fileId);
    setSelectedFiles(updatedFiles);
    onFilesUploaded(updatedFiles);
  };

  return (
    <div className="space-y-3">
      <div className="relative border-2 border-dashed border-neutral/30 rounded-md p-6 text-center hover:border-primary transition-colors">
        {ICONS.UPLOAD}
        <p className="mt-2 text-sm text-neutral/70">
          Drag & drop files here, or click to select files.
        </p>
        <p className="text-xs text-neutral/50">(This is a conceptual uploader)</p>
        <input 
            type="file" 
            multiple 
            onChange={handleSimulatedFileSelect}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            aria-label="Upload files"
        />
      </div>
      {selectedFiles.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-neutral/80">Selected files:</h4>
          <ul className="mt-2 space-y-1 text-sm text-neutral/70 max-h-32 overflow-y-auto">
            {selectedFiles.map(file => (
              <li key={file.id} className="flex justify-between items-center p-1 bg-secondary rounded">
                <span>{file.name} ({Math.round(file.size / 1024)} KB)</span>
                <button 
                    type="button" 
                    onClick={() => removeFile(file.id)} 
                    className="text-error hover:text-error/70 text-xs"
                    aria-label={`Remove ${file.name}`}
                >
                  Remove
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
      <p className="text-xs text-neutral/50">
        For demonstration, if no files are chosen, default conceptual files will be assumed for AI analysis.
      </p>
    </div>
  );
};

export default FileUpload;
    