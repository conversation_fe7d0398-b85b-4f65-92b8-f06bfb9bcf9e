
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { APP_NAME, ICONS } from '../constants';

const Header: React.FC = () => {
  return (
    <header className="bg-base-100 shadow-md sticky top-0 z-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <Link to="/" className="flex items-center space-x-2 text-2xl font-bold text-primary hover:opacity-80 transition-opacity">
            <span className="text-accent">{ICONS.AI_SPARKLE}</span>
            <span>{APP_NAME}</span>
          </Link>
          <nav className="flex space-x-4">
            <Link to="/" className="text-neutral hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">
              Dashboard
            </Link>
            {/* <Link to="/portfolio" className="text-neutral hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">
              Portfolio Insights
            </Link> */}
             <Link to="/how-ai-works" className="text-neutral hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">
              AI Capabilities
            </Link>
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Header;
    