
import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON>hRouter } from 'react-router-dom';
import App from './src/App'; // Assuming App.tsx is also in src
import { ProjectsProvider } from './src/hooks/useProjects'; // Assuming useProjects.ts is in src/hooks
import { NotificationProvider } from './src/contexts/NotificationContext'; // Added
import { i18nPromise } from './src/i18n.ts'; // Import the promise

const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error("Could not find root element to mount to");
}

const root = ReactDOM.createRoot(rootElement);

async function main() {
  try {
    await i18nPromise; // Wait for i18next to be initialized
    root.render(
      <React.StrictMode>
        <HashRouter>
          <NotificationProvider> {/* NotificationProvider now wraps ProjectsProvider */}
            <ProjectsProvider>
              <App />
            </ProjectsProvider>
          </NotificationProvider>
        </HashRouter>
      </React.StrictMode>
    );
  } catch (error) {
    console.error("Failed to initialize i18next or render the application:", error);
    // Optionally render an error message to the user
    rootElement.innerHTML = 'Error initializing the application. Please try again later.';
  }
}

main();